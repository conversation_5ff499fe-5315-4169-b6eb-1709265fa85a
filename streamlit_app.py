# ✅ Streamlit UI App with Auth (YAML-based) + Modern Layout + Secure Hashing + Account Creation
# Compatible with multiple streamlit-authenticator versions

import streamlit as st
from pathlib import Path
import yaml
from yaml.loader import <PERSON><PERSON>oader
import streamlit_authenticator as stauth
import bcrypt

# ---------- PAGE CONFIG (Must be first Streamlit command) ---------- #
st.set_page_config(page_title="Test Case Generator", page_icon="🔧", layout="wide")

# ---------- USER AUTHENTICATION FROM YAML ---------- #
try:
    with open("config.yaml") as file:
        config = yaml.load(file, Loader=SafeLoader)
except FileNotFoundError:
    st.error("❌ config.yaml not found. Please create the config file first.")
    st.stop()

print("Loaded config:", config)

# ---------- HELPER FUNCTIONS ---------- #
def save_config(config):
    """Save the updated config back to the YAML file"""
    try:
        with open('config.yaml', 'w') as file:
            print("Saving config:", config)
            yaml.dump(config, file, default_flow_style=False)
        return True
    except Exception as e:
        st.error(f"❌ Error saving config: {e}")
        return False

def verify_password(password, hashed_password):
    """Verify password against hashed password"""
    try:
        # Use bcrypt to verify the password
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        print(f"Error verifying password: {e}")
        return False

def hash_password(password):
    """Hash a password"""
    return stauth.Hasher([password]).generate()[0]

# ---------- SESSION STATE INITIALIZATION ---------- #
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'username' not in st.session_state:
    st.session_state.username = None
if 'name' not in st.session_state:
    st.session_state.name = None

# ---------- AUTHENTICATION LOGIC ---------- #
def authenticate_user(username, password):
    """Authenticate user with username and password"""
    if username in config['credentials']['usernames']:
        stored_password = config['credentials']['usernames'][username]['password']
        if verify_password(password, stored_password):
            st.session_state.authenticated = True
            st.session_state.username = username
            st.session_state.name = config['credentials']['usernames'][username]['name']
            return True
    return False

def logout_user():
    """Logout user"""
    st.session_state.authenticated = False
    st.session_state.username = None
    st.session_state.name = None

# ---------- MAIN APP LOGIC ---------- #
if not st.session_state.authenticated:
    # ---------- SHOW LOGIN/REGISTRATION INTERFACE ---------- #
    st.title("🔧 Test Case Generator")
    st.markdown("---")

    # Create tabs for Login and Create Account
    tab1, tab2 = st.tabs(["🔐 Login", "📝 Create Account"])

    with tab1:
        st.subheader("🔐 Login to Your Account")

        with st.form("login_form", clear_on_submit=False):
            username_input = st.text_input("Username")
            password_input = st.text_input("Password", type="password")
            login_button = st.form_submit_button("🔑 Login")

            if login_button:
                if username_input and password_input:
                    if authenticate_user(username_input, password_input):
                        st.success("✅ Login successful!")
                        st.rerun()
                    else:
                        st.error("❌ Invalid username or password")
                else:
                    st.warning("⚠️ Please enter both username and password")

    with tab2:
        st.subheader("🆕 Create New Account")
        st.info("Fill out the form below to create your account")

        with st.form("registration_form", clear_on_submit=True):
            reg_name = st.text_input("Full Name", placeholder="Enter your full name")
            reg_username = st.text_input("Username", placeholder="Choose a username")
            reg_email = st.text_input("Email", placeholder="Enter your email address")
            reg_password = st.text_input("Password", type="password", placeholder="Create a password")
            reg_password_repeat = st.text_input("Confirm Password", type="password", placeholder="Repeat your password")
            register_button = st.form_submit_button("🆕 Create Account")

            if register_button:
                # Validation
                if not all([reg_name, reg_username, reg_email, reg_password, reg_password_repeat]):
                    st.error("❌ Please fill in all fields")
                elif reg_password != reg_password_repeat:
                    st.error("❌ Passwords do not match")
                elif reg_username in config['credentials']['usernames']:
                    st.error("❌ Username already exists. Please choose a different username.")
                elif len(reg_password) < 6:
                    st.error("❌ Password must be at least 6 characters long")
                else:
                    # Create new user
                    hashed_password = hash_password(reg_password)

                    # Add to config
                    config['credentials']['usernames'][reg_username] = {
                        'name': reg_name,
                        'password': hashed_password,
                        'email': reg_email
                    }

                    # Save config
                    if save_config(config):
                        st.success('✅ Account created successfully!')
                        st.info('🔄 Please switch to the Login tab to sign in with your new account.')
                        st.balloons()
                    else:
                        st.error('❌ Failed to save account. Please try again.')

else:
    # ---------- AUTHENTICATED USER SECTION ---------- #

    # ---------- CUSTOM STYLES ---------- #
    st.markdown("""
        <style>
            .main-title {
                font-size: 3rem;
                font-weight: 700;
                color: #4F8BF9;
            }
            .sub-title {
                font-size: 1.2rem;
                color: #444;
            }
            .stTextInput>div>div>input {
                border-radius: 8px;
            }
            .stButton>button {
                border-radius: 10px;
                background-color: #4F8BF9;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
            }
        </style>
    """, unsafe_allow_html=True)

    # ---------- HEADER SECTION ---------- #
    st.markdown("<p class='main-title'>🔧 AI-Powered Test Case Generator</p>", unsafe_allow_html=True)
    st.markdown("<p class='sub-title'>Automatically generate test cases from your requirement documents using hybrid RAG (Neo4j + Milvus)</p>", unsafe_allow_html=True)

    # ---------- SIDEBAR: USER + PROJECT ---------- #
    with st.sidebar:
        st.write(f'Welcome *{st.session_state.name}*')

        # Logout button
        if st.button("🚪 Logout"):
            logout_user()
            st.rerun()

        st.header("🧠 Project Info")
        project_id = st.text_input("Enter your Project Name")
        st.markdown("---")
        st.info("Your documents and indexes will be stored securely under your user ID.")

    # ---------- MAIN PANEL ---------- #
    col1, col2 = st.columns([2, 1])

    with col1:
        uploaded_file = st.file_uploader("Upload Requirements Document (.pdf, .docx, .txt)", type=["pdf", "docx", "txt"])
        query = st.text_input("Optional: Enter Query (e.g., 'login functionality')")
        generate_btn = st.button("📝 Generate Test Cases")

    with col2:
        st.subheader("📂 Project Preview")
        st.markdown(f"User ID: `{st.session_state.username}`\nProject: `{project_id}`")
        st.markdown("---")
        st.markdown("Upload your document, enter an optional search query, then click **Generate**.")

    # ---------- PROCESS ---------- #
    if generate_btn:
        if not st.session_state.username or not project_id or not uploaded_file:
            st.error("🚫 Please provide Project Name and upload a document.")
        else:
            # Save uploaded file
            user_dir = Path(f"users/{st.session_state.username}/{project_id}")
            user_dir.mkdir(parents=True, exist_ok=True)
            file_path = user_dir / uploaded_file.name
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())

            # Stub processing
            st.success("✅ Document uploaded. Beginning processing...")
            st.info("(Processing logic to be connected here: chunking, Neo4j, Milvus, LLM)")

            # Placeholder test case output
            st.markdown("""
            ### ✅ Sample Generated Test Case
            - **Requirement**: The user must be able to log in using email and password.
            - **Test Type**: Functional
            - **Preconditions**: User is registered.
            - **Test Steps**:
              1. Go to login page
              2. Enter valid credentials
              3. Click login
            - **Expected Result**: User is logged in and redirected to the dashboard
            """)

            st.download_button("📥 Download Markdown", """### Test Case Title
- **Requirement**: Sample
- **Steps**: ...
""", file_name="test_cases.md")