"""
Test case service for generating and managing test cases.
"""
import json
import re
from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import HTTPEx<PERSON>, status
from llama_index.llms.openai import OpenAI

from ..models import TestCase, Requirement, Project, TestCaseType, TestCasePriority
from ..schemas import TestCaseCreate, TestCaseGeneration
from ..core.config import settings
from ..utils.logger import logger
from .llm_factory import LLMFactory


class TestCaseService:
    """Service for handling test case operations."""
    
    def __init__(self):
        """Initialize the test case service."""
        # self.llm = OpenAI(
        #     temperature=settings.OPENAI_TEMPERATURE,
        #     model=settings.OPENAI_MODEL,
        #     api_key=settings.OPENAI_API_KEY
        # )
        try:
            self.llm = LLMFactory.create_llm()
            provider_info = LLMFactory.get_provider_info()
            logger.info(f"Initialized LLM: {provider_info}")
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            self.llm = None
    
    def generate_test_cases(
        self, 
        db: Session, 
        generation_request: TestCaseGeneration, 
        user_id: int
    ) -> List[TestCase]:
        """
        Generate test cases for a project.
        
        Args:
            db: Database session
            generation_request: Test case generation request
            user_id: User ID
            
        Returns:
            List of generated test cases
        """

        # Check if LLM is available
        if not self.llm:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="LLM service is not available. Please check configuration."
            )
        
        # Verify LLM configuration
        if not settings.validate_llm_config():
            provider = settings.LLM_PROVIDER.value
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail=f"LLM provider '{provider}' is not properly configured. Please check API keys."
            )
        
        # Verify project access
        project = db.query(Project).filter(
            Project.id == generation_request.project_id,
            Project.owner_id == user_id
        ).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        # Get requirements for the project
        requirements = self._get_project_requirements(db, generation_request.project_id)
        
        if not requirements:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No requirements found for this project. Please upload and process documents first."
            )
        
        # Generate test cases
        all_test_cases = []
        for requirement in requirements:
            try:
                test_cases = self._generate_test_cases_for_requirement(
                    requirement, 
                    generation_request.num_test_cases
                )
                
                # Save test cases to database
                for tc_data in test_cases:
                    test_case = TestCase(
                        test_case_id=tc_data["id"],
                        title=tc_data["title"],
                        description=tc_data.get("description"),
                        preconditions=tc_data.get("preconditions"),
                        test_steps=tc_data.get("test_steps"),
                        expected_result=tc_data.get("expected_result"),
                        test_type=TestCaseType.FUNCTIONAL,
                        priority=TestCasePriority.MEDIUM,
                        project_id=generation_request.project_id,
                        requirement_id=requirement.id,
                        created_by=user_id
                    )
                    db.add(test_case)
                    all_test_cases.append(test_case)
                
                logger.info(f"Generated {len(test_cases)} test cases for requirement {requirement.requirement_id}")
                
            except Exception as e:
                logger.error(f"Error generating test cases for requirement {requirement.requirement_id}: {e}")
                # Create fallback test case
                fallback_tc = self._create_fallback_test_case(
                    requirement, 
                    generation_request.project_id, 
                    user_id
                )
                db.add(fallback_tc)
                all_test_cases.append(fallback_tc)
        
        db.commit()
        
        # Refresh all test cases
        for tc in all_test_cases:
            db.refresh(tc)
        
        logger.info(f"Generated {len(all_test_cases)} total test cases for project {generation_request.project_id}")
        return all_test_cases
    
    def get_project_test_cases(self, db: Session, project_id: int, user_id: int) -> List[TestCase]:
        """
        Get all test cases for a project.
        
        Args:
            db: Database session
            project_id: Project ID
            user_id: User ID
            
        Returns:
            List of test cases
        """
        return db.query(TestCase).join(Project).filter(
            TestCase.project_id == project_id,
            Project.owner_id == user_id
        ).all()
    
    def create_test_case(self, db: Session, test_case_data: TestCaseCreate, user_id: int) -> TestCase:
        """
        Create a new test case manually.
        
        Args:
            db: Database session
            test_case_data: Test case creation data
            user_id: User ID
            
        Returns:
            Created test case
        """
        test_case = TestCase(
            **test_case_data.dict(),
            created_by=user_id
        )
        
        db.add(test_case)
        db.commit()
        db.refresh(test_case)
        
        logger.info(f"Created test case: {test_case.test_case_id}")
        return test_case
    
    def export_test_cases(self, db: Session, project_id: int, user_id: int, format: str = "markdown") -> str:
        """
        Export test cases in specified format.
        
        Args:
            db: Database session
            project_id: Project ID
            user_id: User ID
            format: Export format (markdown, json, csv)
            
        Returns:
            Exported content as string
        """
        test_cases = self.get_project_test_cases(db, project_id, user_id)
        
        if format == "markdown":
            return self._export_to_markdown(test_cases)
        elif format == "json":
            return self._export_to_json(test_cases)
        elif format == "csv":
            return self._export_to_csv(test_cases)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported export format"
            )
    
    def _get_project_requirements(self, db: Session, project_id: int) -> List[Requirement]:
        """Get all requirements for a project."""
        return db.query(Requirement).join(
            Requirement.document
        ).filter(
            Requirement.document.has(project_id=project_id)
        ).all()
    
    def _generate_test_cases_for_requirement(self, requirement: Requirement, num_cases: int) -> List[dict]:
        """Generate test cases for a specific requirement using LLM."""
        prompt = f"""
You are a senior QA engineer tasked with creating test cases for a software system. 
Generate {num_cases} test cases for the following requirement:

REQUIREMENT ID: {requirement.requirement_id}
DESCRIPTION: {requirement.description}

For each test case, provide:
1. Test Case ID (format: TC{requirement.requirement_id.split('-')[1]}.X, e.g., TC1.1, TC1.2)
2. Test Case Title (descriptive, concise, starting with 'Verify', 'Confirm', or 'Test')
3. Description (brief explanation of what the test validates)
4. Preconditions (what must be true before executing the test)
5. Test Steps (numbered steps to execute the test)
6. Expected Result (what should happen when the test passes)

Focus on:
- Positive test scenarios (valid inputs, expected behavior)
- Negative test scenarios (invalid inputs, error handling)
- Edge cases (boundary values, extreme conditions)

Format your response as JSON:
{{
    "test_cases": [
        {{
            "id": "TC{requirement.requirement_id.split('-')[1]}.1",
            "title": "Verify that a new user can successfully register with valid information",
            "description": "Test the user registration functionality with valid input data",
            "preconditions": "User is not already registered",
            "test_steps": "1. Navigate to registration page\\n2. Enter valid user details\\n3. Click register button",
            "expected_result": "User is successfully registered and redirected to login page"
        }}
    ]
}}
"""
        
        try:
            response = self.llm.complete(prompt)
            return self._parse_test_case_response(response.text, requirement)
        except Exception as e:
            logger.error(f"Error calling LLM for requirement {requirement.requirement_id}: {e}")
            raise
    
    def _parse_test_case_response(self, response_text: str, requirement: Requirement) -> List[dict]:
        """Parse LLM response to extract test cases."""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_data = json.loads(json_match.group())
                test_cases = []
                
                for idx, tc_data in enumerate(json_data.get('test_cases', []), 1):
                    tc_id = tc_data.get('id', f"TC{requirement.requirement_id.split('-')[1]}.{idx}")
                    # Remove leading zeros
                    tc_id = re.sub(r'\.0(\d)$', r'.\1', tc_id)
                    
                    test_cases.append({
                        "id": tc_id,
                        "title": tc_data.get('title', 'Generated Test Case'),
                        "description": tc_data.get('description'),
                        "preconditions": tc_data.get('preconditions'),
                        "test_steps": tc_data.get('test_steps'),
                        "expected_result": tc_data.get('expected_result')
                    })
                
                return test_cases
            else:
                raise ValueError("No JSON found in response")
                
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"Error parsing test case response: {e}")
            # Return fallback test case data
            return [{
                "id": f"TC{requirement.requirement_id.split('-')[1]}.1",
                "title": f"Verify {requirement.description[:50]}...",
                "description": f"Test case for requirement {requirement.requirement_id}",
                "preconditions": "System is ready for testing",
                "test_steps": "1. Execute the requirement functionality",
                "expected_result": "Requirement is satisfied"
            }]
    
    def _create_fallback_test_case(self, requirement: Requirement, project_id: int, user_id: int) -> TestCase:
        """Create a fallback test case when generation fails."""
        return TestCase(
            test_case_id=f"TC{requirement.requirement_id.split('-')[1]}.1",
            title=f"Verify {requirement.description[:50]}...",
            description=f"Test case for requirement {requirement.requirement_id}",
            project_id=project_id,
            requirement_id=requirement.id,
            created_by=user_id
        )
    
    def _export_to_markdown(self, test_cases: List[TestCase]) -> str:
        """Export test cases to markdown format grouped by requirements."""
        content = "# Test Cases Report\n\n"

        # Group test cases by requirement
        requirements_map = {}
        for tc in test_cases:
            if tc.requirement:
                req_id = tc.requirement.requirement_id
                if req_id not in requirements_map:
                    requirements_map[req_id] = {
                        'requirement': tc.requirement,
                        'test_cases': []
                    }
                requirements_map[req_id]['test_cases'].append(tc)
            else:
                # Handle test cases without requirements
                if 'NO_REQUIREMENT' not in requirements_map:
                    requirements_map['NO_REQUIREMENT'] = {
                        'requirement': None,
                        'test_cases': []
                    }
                requirements_map['NO_REQUIREMENT']['test_cases'].append(tc)

        # Generate content grouped by requirements
        for req_id, req_data in requirements_map.items():
            if req_data['requirement']:
                content += f"## {req_data['requirement'].requirement_id}: {req_data['requirement'].description}\n\n"
            else:
                content += f"## Manual Test Cases\n\n"

            content += "### Test Cases:\n\n"

            for tc in req_data['test_cases']:
                content += f"#### {tc.test_case_id}: {tc.title}\n\n"
                if tc.description:
                    content += f"**Description:** {tc.description}\n\n"
                if tc.preconditions:
                    content += f"**Preconditions:** {tc.preconditions}\n\n"
                if tc.test_steps:
                    content += f"**Test Steps:**\n{tc.test_steps}\n\n"
                if tc.expected_result:
                    content += f"**Expected Result:** {tc.expected_result}\n\n"
                if tc.test_type:
                    content += f"**Type:** {tc.test_type.value}\n\n"
                if tc.priority:
                    content += f"**Priority:** {tc.priority.value}\n\n"
                content += "---\n\n"

            content += "\n"

        return content
    
    def _export_to_json(self, test_cases: List[TestCase]) -> str:
        """Export test cases to JSON format grouped by requirements."""
        # Group test cases by requirement
        requirements_map = {}
        for tc in test_cases:
            if tc.requirement:
                req_id = tc.requirement.requirement_id
                if req_id not in requirements_map:
                    requirements_map[req_id] = {
                        'requirement': {
                            'id': tc.requirement.requirement_id,
                            'description': tc.requirement.description
                        },
                        'test_cases': []
                    }
                requirements_map[req_id]['test_cases'].append({
                    "id": tc.test_case_id,
                    "title": tc.title,
                    "description": tc.description,
                    "preconditions": tc.preconditions,
                    "test_steps": tc.test_steps,
                    "expected_result": tc.expected_result,
                    "type": tc.test_type.value if tc.test_type else None,
                    "priority": tc.priority.value if tc.priority else None
                })
            else:
                # Handle test cases without requirements
                if 'manual_test_cases' not in requirements_map:
                    requirements_map['manual_test_cases'] = {
                        'requirement': {
                            'id': 'MANUAL',
                            'description': 'Manually created test cases'
                        },
                        'test_cases': []
                    }
                requirements_map['manual_test_cases']['test_cases'].append({
                    "id": tc.test_case_id,
                    "title": tc.title,
                    "description": tc.description,
                    "preconditions": tc.preconditions,
                    "test_steps": tc.test_steps,
                    "expected_result": tc.expected_result,
                    "type": tc.test_type.value if tc.test_type else None,
                    "priority": tc.priority.value if tc.priority else None
                })

        # Convert to list format
        data = []
        for req_data in requirements_map.values():
            data.append(req_data)

        return json.dumps(data, indent=2)
    
    def _export_to_csv(self, test_cases: List[TestCase]) -> str:
        """Export test cases to CSV format with requirement information."""
        import csv
        import io

        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            "Requirement ID", "Requirement Description", "Test Case ID", "Title",
            "Description", "Preconditions", "Test Steps", "Expected Result", "Type", "Priority"
        ])

        # Write data
        for tc in test_cases:
            req_id = tc.requirement.requirement_id if tc.requirement else "MANUAL"
            req_desc = tc.requirement.description if tc.requirement else "Manually created test case"

            writer.writerow([
                req_id,
                req_desc,
                tc.test_case_id,
                tc.title,
                tc.description or "",
                tc.preconditions or "",
                tc.test_steps or "",
                tc.expected_result or "",
                tc.test_type.value if tc.test_type else "",
                tc.priority.value if tc.priority else ""
            ])

        return output.getvalue()
