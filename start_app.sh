#!/bin/bash

echo "Starting Test Case Generator Application..."
echo

echo "Activating conda environment..."
source activate testcasegen

echo
echo "Starting Backend API Server..."
cd backend
python -m uvicorn app.main:app --reload --port 8000 &
BACKEND_PID=$!
cd ..

echo "Waiting for backend to start..."
sleep 5

echo
echo "Starting Frontend Streamlit App..."
cd frontend
streamlit run streamlit_app.py --server.port 8501 &
FRONTEND_PID=$!
cd ..

echo
echo "Application started successfully!"
echo "Backend API: http://localhost:8000"
echo "API Documentation: http://localhost:8000/docs"
echo "Frontend App: http://localhost:8501"
echo
echo "Press Ctrl+C to stop all services..."

# Wait for user interrupt
trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
