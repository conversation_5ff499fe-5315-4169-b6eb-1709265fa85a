"""
Test case management API endpoints.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Response
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...schemas import (
    TestCase, TestCaseCreate, TestCaseUpdate, 
    TestCaseGeneration, TestCaseGenerationResponse, TestCaseExport
)
from ...services import TestCaseService
from ...api.v1.auth import get_current_active_user
from ...utils.logger import logger

from ...services.llm_factory import LLMFactory
from ...core.config import settings

router = APIRouter()

@router.get("/llm-status")
async def get_llm_status():
    """Get current LLM provider status."""
    try:
        provider_info = LLMFactory.get_provider_info()
        is_configured = settings.validate_llm_config()
        
        return {
            "provider_info": provider_info,
            "is_configured": is_configured,
            "current_provider": settings.LLM_PROVIDER.value
        }
    except Exception as e:
        logger.error(f"Error getting LLM status: {e}")
        return {
            "provider_info": {},
            "is_configured": False,
            "error": str(e)
        }

@router.post("/generate", response_model=TestCaseGenerationResponse)
async def generate_test_cases(
    generation_request: TestCaseGeneration,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Generate test cases for a project.
    
    Args:
        generation_request: Test case generation parameters
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Generated test cases
    """
    service = TestCaseService()
    
    try:
        test_cases = service.generate_test_cases(db, generation_request, current_user.id)
        
        logger.info(f"Generated {len(test_cases)} test cases for project {generation_request.project_id}")
        
        return TestCaseGenerationResponse(
            message="Test cases generated successfully",
            project_id=generation_request.project_id,
            generated_count=len(test_cases),
            test_cases=test_cases
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating test cases: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating test cases"
        )


@router.get("/project/{project_id}", response_model=List[TestCase])
async def get_project_test_cases(
    project_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all test cases for a project.
    
    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of test cases
    """
    service = TestCaseService()
    test_cases = service.get_project_test_cases(db, project_id, current_user.id)
    return test_cases


@router.post("/", response_model=TestCase, status_code=status.HTTP_201_CREATED)
async def create_test_case(
    test_case_data: TestCaseCreate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new test case manually.
    
    Args:
        test_case_data: Test case creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Created test case
    """
    service = TestCaseService()
    test_case = service.create_test_case(db, test_case_data, current_user.id)
    
    logger.info(f"Test case created: {test_case.test_case_id} by user {current_user.username}")
    return test_case


@router.get("/{test_case_id}", response_model=TestCase)
async def get_test_case(
    test_case_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific test case.
    
    Args:
        test_case_id: Test case ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Test case details
    """
    from ...models import TestCase as TestCaseModel, Project
    
    test_case = db.query(TestCaseModel).join(Project).filter(
        TestCaseModel.id == test_case_id,
        Project.owner_id == current_user.id
    ).first()
    
    if not test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test case not found"
        )
    
    return test_case


@router.put("/{test_case_id}", response_model=TestCase)
async def update_test_case(
    test_case_id: int,
    test_case_update: TestCaseUpdate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update a test case.
    
    Args:
        test_case_id: Test case ID
        test_case_update: Test case update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated test case
    """
    from ...models import TestCase as TestCaseModel, Project
    
    test_case = db.query(TestCaseModel).join(Project).filter(
        TestCaseModel.id == test_case_id,
        Project.owner_id == current_user.id
    ).first()
    
    if not test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test case not found"
        )
    
    update_data = test_case_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(test_case, field, value)
    
    db.commit()
    db.refresh(test_case)
    
    logger.info(f"Test case updated: {test_case.test_case_id} by user {current_user.username}")
    return test_case


@router.delete("/{test_case_id}")
async def delete_test_case(
    test_case_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a test case.
    
    Args:
        test_case_id: Test case ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    from ...models import TestCase as TestCaseModel, Project
    
    test_case = db.query(TestCaseModel).join(Project).filter(
        TestCaseModel.id == test_case_id,
        Project.owner_id == current_user.id
    ).first()
    
    if not test_case:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Test case not found"
        )
    
    db.delete(test_case)
    db.commit()
    
    logger.info(f"Test case deleted: {test_case.test_case_id} by user {current_user.username}")
    return {"message": "Test case deleted successfully"}


@router.post("/export")
async def export_test_cases(
    export_request: TestCaseExport,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Export test cases in specified format.
    
    Args:
        export_request: Export parameters
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Exported content
    """
    service = TestCaseService()
    
    try:
        content = service.export_test_cases(
            db, 
            export_request.project_id, 
            current_user.id, 
            export_request.format
        )
        
        # Set appropriate content type and filename
        if export_request.format == "markdown":
            media_type = "text/markdown"
            filename = f"test_cases_{export_request.project_id}.md"
        elif export_request.format == "json":
            media_type = "application/json"
            filename = f"test_cases_{export_request.project_id}.json"
        elif export_request.format == "csv":
            media_type = "text/csv"
            filename = f"test_cases_{export_request.project_id}.csv"
        else:
            media_type = "text/plain"
            filename = f"test_cases_{export_request.project_id}.txt"
        
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting test cases: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error exporting test cases"
        )
