"""
Streamlit frontend for the Test Case Generator application.
This version uses the FastAPI backend for all operations.
"""
import streamlit as st
import requests
import json
from pathlib import Path
from typing import Optional, Dict, Any

# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

# Page config
st.set_page_config(
    page_title="Test Case Generator", 
    page_icon="🔧", 
    layout="wide"
)

# Session state initialization
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_info' not in st.session_state:
    st.session_state.user_info = None
if 'current_project' not in st.session_state:
    st.session_state.current_project = None

# API Helper Functions
def make_api_request(
    method: str, 
    endpoint: str, 
    data: Optional[Dict] = None, 
    files: Optional[Dict] = None,
    headers: Optional[Dict] = None
) -> Optional[Dict[str, Any]]:
    """Make API request with error handling."""
    url = f"{API_BASE_URL}{endpoint}"
    
    # Add authorization header if token exists
    if st.session_state.access_token and headers is None:
        headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
    elif st.session_state.access_token and headers:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            if files:
                response = requests.post(url, data=data, files=files, headers=headers)
            else:
                response = requests.post(url, json=data, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            st.error(f"Unsupported HTTP method: {method}")
            return None
        
        if response.status_code == 200 or response.status_code == 201:
            return response.json()
        elif response.status_code == 401:
            st.error("Authentication failed. Please login again.")
            logout_user()
            return None
        else:
            error_detail = response.json().get("detail", "Unknown error") if response.content else "Unknown error"
            st.error(f"API Error: {error_detail}")
            return None
            
    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to the backend server. Please ensure the API server is running on http://localhost:8000")
        return None
    except Exception as e:
        st.error(f"Request failed: {str(e)}")
        return None

def login_user(username: str, password: str) -> bool:
    """Login user and store token."""
    data = {"username": username, "password": password}
    response = make_api_request("POST", "/auth/login", data=data, headers={})
    
    if response:
        st.session_state.access_token = response["access_token"]
        # Get user info
        user_info = make_api_request("GET", "/auth/me")
        if user_info:
            st.session_state.user_info = user_info
            return True
    
    return False

def register_user(username: str, email: str, full_name: str, password: str) -> bool:
    """Register new user."""
    data = {
        "username": username,
        "email": email,
        "full_name": full_name,
        "password": password
    }
    response = make_api_request("POST", "/auth/register", data=data, headers={})
    return response is not None

def logout_user():
    """Logout user and clear session."""
    st.session_state.access_token = None
    st.session_state.user_info = None
    st.session_state.current_project = None

def get_user_projects():
    """Get user's projects."""
    return make_api_request("GET", "/projects/")

def create_project(name: str, description: str = ""):
    """Create new project."""
    data = {"name": name, "description": description}
    return make_api_request("POST", "/projects/", data=data)

def delete_project(project_id: int):
    """Delete a project."""
    response = make_api_request("DELETE", f"/projects/{project_id}")
    return response is not None  # Returns True if successful

def upload_document(project_id: int, file):
    """Upload document to project."""
    files = {"file": (file.name, file, file.type)}
    return make_api_request("POST", f"/projects/{project_id}/documents", files=files)

def process_document(project_id: int, document_id: int):
    """Process document to extract requirements."""
    return make_api_request("POST", f"/projects/{project_id}/documents/{document_id}/process")

def delete_document(project_id: int, document_id: int):
    """Delete a document."""
    response = make_api_request("DELETE", f"/projects/{project_id}/documents/{document_id}")
    return response is not None  # Returns True if successful

def generate_test_cases(project_id: int, num_test_cases: int = 5, query: str = ""):
    """Generate test cases for project."""
    data = {
        "project_id": project_id,
        "num_test_cases": num_test_cases,
        "query": query if query else None
    }
    return make_api_request("POST", "/test-cases/generate", data=data)

def get_project_test_cases(project_id: int):
    """Get test cases for project."""
    return make_api_request("GET", f"/test-cases/project/{project_id}")

def export_test_cases(project_id: int, format: str = "markdown"):
    """Export test cases in specified format."""
    data = {
        "project_id": project_id,
        "format": format
    }

    url = f"{API_BASE_URL}/test-cases/export"
    headers = {"Authorization": f"Bearer {st.session_state.access_token}"}

    try:
        response = requests.post(url, json=data, headers=headers)
        if response.status_code == 200:
            return response.text
        else:
            st.error(f"Export failed: {response.json().get('detail', 'Unknown error')}")
            return None
    except Exception as e:
        st.error(f"Export request failed: {str(e)}")
        return None

# Custom CSS
st.markdown("""
    <style>
        .main-title {
            font-size: 3rem;
            font-weight: 700;
            color: #4F8BF9;
        }
        .sub-title {
            font-size: 1.2rem;
            color: #444;
        }
        .stButton>button {
            border-radius: 10px;
            background-color: #4F8BF9;
            color: white;
            font-weight: bold;
            padding: 10px 20px;
        }
        .project-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            background-color: #f9f9f9;
        }
    </style>
""", unsafe_allow_html=True)

# Main Application Logic
if not st.session_state.access_token:
    # Authentication UI
    st.markdown("<p class='main-title'>🔧 Test Case Generator</p>", unsafe_allow_html=True)
    st.markdown("---")
    
    tab1, tab2 = st.tabs(["🔐 Login", "📝 Create Account"])
    
    with tab1:
        st.subheader("🔐 Login to Your Account")
        
        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            login_button = st.form_submit_button("🔑 Login")
            
            if login_button:
                if username and password:
                    if login_user(username, password):
                        st.success("✅ Login successful!")
                        st.rerun()
                    else:
                        st.error("❌ Invalid username or password")
                else:
                    st.warning("⚠️ Please enter both username and password")
    
    with tab2:
        st.subheader("🆕 Create New Account")
        
        with st.form("registration_form"):
            full_name = st.text_input("Full Name")
            username = st.text_input("Username")
            email = st.text_input("Email")
            password = st.text_input("Password", type="password")
            password_confirm = st.text_input("Confirm Password", type="password")
            register_button = st.form_submit_button("🆕 Create Account")
            
            if register_button:
                if not all([full_name, username, email, password, password_confirm]):
                    st.error("❌ Please fill in all fields")
                elif password != password_confirm:
                    st.error("❌ Passwords do not match")
                elif len(password) < 6:
                    st.error("❌ Password must be at least 6 characters long")
                else:
                    if register_user(username, email, full_name, password):
                        st.success("✅ Account created successfully!")
                        st.info("🔄 Please switch to the Login tab to sign in.")
                        st.balloons()
                    else:
                        st.error("❌ Registration failed. Username or email may already exist.")

else:
    # Authenticated user interface
    st.markdown("<p class='main-title'>🔧 AI-Powered Test Case Generator</p>", unsafe_allow_html=True)
    st.markdown("<p class='sub-title'>Generate test cases from requirements using AI</p>", unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.write(f"Welcome **{st.session_state.user_info['full_name']}**")
        
        if st.button("🚪 Logout"):
            logout_user()
            st.rerun()
        
        st.markdown("---")
        
        # Project selection
        st.subheader("📁 Projects")
        projects = get_user_projects()
        
        if projects:
            project_names = [f"{p['name']} (ID: {p['id']})" for p in projects]
            # selected_project_idx = st.selectbox(
            #     "Select Project",
            #     range(len(projects)),
            #     format_func=lambda x: project_names[x],
            #     key="project_selector"
            # )
            # st.session_state.current_project = projects[selected_project_idx]
            selected_project_name = st.selectbox("Select Project:", project_names)
        
            if selected_project_name:
                selected_project = next(p for p in projects if p['name'] == selected_project_name)
                st.session_state.current_project = selected_project
                
                # Project actions
                col1, col2 = st.columns(2)
                
                with col1:
                    if st.button("📝 Manage", key="manage_project"):
                        st.session_state.show_project_details = True
                
                with col2:
                    if st.button("🗑️ Delete", key="delete_project", type="secondary"):
                        st.session_state.show_delete_confirmation = True
                
                # Delete confirmation dialog
                if st.session_state.get('show_delete_confirmation', False):
                    st.warning("⚠️ **Delete Project?**")
                    st.write(f"This will permanently delete **{selected_project['name']}** and all its:")
                    st.write("• Documents")
                    st.write("• Requirements") 
                    st.write("• Test Cases")
                    
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        if st.button("✅ Yes, Delete", key="confirm_delete", type="primary"):
                            if delete_project(selected_project['id']):
                                st.success("Project deleted successfully!")
                                st.session_state.current_project = None
                                st.session_state.show_delete_confirmation = False
                                st.rerun()
                            else:
                                st.error("Failed to delete project")
                    
                    with col2:
                        if st.button("❌ Cancel", key="cancel_delete"):
                            st.session_state.show_delete_confirmation = False
                            st.rerun()
        else:
            st.info("No projects found. Create one below.")
            st.session_state.current_project = None
        
        # Create new project
        st.subheader("➕ Create Project")
        with st.form("create_project_form"):
            project_name = st.text_input("Project Name")
            project_desc = st.text_area("Description (optional)")
            create_btn = st.form_submit_button("Create Project")
            
            if create_btn and project_name:
                if create_project(project_name, project_desc):
                    st.success("Project created!")
                    st.rerun()
                else:
                    st.error("Failed to create project")
    
    # Main content
    if st.session_state.current_project:
        project = st.session_state.current_project
        
        st.subheader(f"📁 Project: {project['name']}")
        
        # Tabs for different operations
        tab1, tab2, tab3 = st.tabs(["📄 Documents", "🔧 Generate Test Cases", "📋 View Test Cases"])
        
        with tab1:
            st.subheader("📄 Document Management")
            
            # Upload document
            uploaded_file = st.file_uploader(
                "Upload Requirements Document", 
                type=["pdf", "docx", "txt"]
            )
            
            if uploaded_file and st.button("📤 Upload Document"):
                with st.spinner("Uploading document..."):
                    result = upload_document(project['id'], uploaded_file)
                    if result:
                        st.success("Document uploaded successfully!")
                        
                        # Process document
                        with st.spinner("Processing document to extract requirements..."):
                            requirements = process_document(project['id'], result['id'])
                            if requirements:
                                st.success(f"Found {len(requirements)} requirements!")
                                
                                # Display requirements
                                st.subheader("📝 Extracted Requirements")
                                for req in requirements:
                                    st.write(f"**{req['requirement_id']}:** {req['description']}")
                            else:
                                st.warning("No requirements found in the document.")

            # Get project documents
            documents = make_api_request("GET", f"/projects/{project['id']}/documents")
            
            if documents:
                for doc in documents:
                    col1, col2, col3, col4 = st.columns([2, 1, 2, 2])
                    
                    with col1:
                        st.write(f"📄 **{doc['original_filename']}**")
                    
                    with col2:
                        # Convert bytes to MB for display
                        size_mb = doc['file_size'] / (1024 * 1024)
                        print(f"File Size: {size_mb} MB")
                        st.write(f"Size: {size_mb:.4f} MB")
                    
                    with col3:
                        st.write(f"Status: {doc['status']}")
                    
                    with col4:
                        if st.button("🗑️ Delete", key=f"delete_doc_{doc['id']}"):
                            st.markdown("## ⚠️ Confirm Deletion")
                            st.markdown("This will also delete all associated requirements and test cases.")
                            confirm_text = st.text_input(f"Type 'delete' to confirm deletion of '{doc['original_filename']}'", key=f"confirm_input_{doc['id']}")

                            col1, col2 = st.columns([1, 1])
                            with col1:
                                if st.button("✅ Delete", key=f"confirm_btn_{doc['id']}") and confirm_text.lower() == "delete":
                                    if delete_document(project['id'], doc['id']):
                                        st.success("✅ Document deleted successfully!")
                                        st.rerun()
                                    else:
                                        st.error("❌ Failed to delete document.")
                            with col2:
                                st.button("❌ Cancel", key=f"cancel_btn_{doc['id']}")
            else:
                st.info("No documents uploaded yet.")
        
        with tab2:
            st.subheader("🔧 Generate Test Cases")
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                query = st.text_input("Optional Query (e.g., 'login functionality')")
                num_cases = st.slider("Number of test cases per requirement", 1, 10, 5)
            
            with col2:
                st.info(f"**Project:** {project['name']}")
            
            if st.button("🚀 Generate Test Cases"):
                with st.spinner("Generating test cases using AI..."):
                    result = generate_test_cases(project['id'], num_cases, query)
                    if result:
                        st.success(f"Generated {result['generated_count']} test cases!")
                        
                        # Display generated test cases
                        st.subheader("✅ Generated Test Cases")
                        for tc in result['test_cases']:
                            with st.expander(f"{tc['test_case_id']}: {tc['title']}"):
                                if tc.get('description'):
                                    st.write(f"**Description:** {tc['description']}")
                                if tc.get('preconditions'):
                                    st.write(f"**Preconditions:** {tc['preconditions']}")
                                if tc.get('test_steps'):
                                    st.write(f"**Test Steps:** {tc['test_steps']}")
                                if tc.get('expected_result'):
                                    st.write(f"**Expected Result:** {tc['expected_result']}")
        
        with tab3:
            st.subheader("📋 View Test Cases")
            
            test_cases = get_project_test_cases(project['id'])
            
            if test_cases:
                st.write(f"Found {len(test_cases)} test cases")
                
                # Export options
                col1, col2, col3 = st.columns(3)
                with col1:
                    if st.button("📥 Export as Markdown"):
                        with st.spinner("Exporting test cases..."):
                            content = export_test_cases(project['id'], "markdown")
                            if content:
                                st.download_button(
                                    label="📥 Download Markdown File",
                                    data=content,
                                    file_name=f"test_cases_{project['name']}.md",
                                    mime="text/markdown"
                                )
                                st.success("✅ Export ready for download!")

                with col2:
                    if st.button("📥 Export as JSON"):
                        with st.spinner("Exporting test cases..."):
                            content = export_test_cases(project['id'], "json")
                            if content:
                                st.download_button(
                                    label="📥 Download JSON File",
                                    data=content,
                                    file_name=f"test_cases_{project['name']}.json",
                                    mime="application/json"
                                )
                                st.success("✅ Export ready for download!")

                with col3:
                    if st.button("📥 Export as CSV"):
                        with st.spinner("Exporting test cases..."):
                            content = export_test_cases(project['id'], "csv")
                            if content:
                                st.download_button(
                                    label="📥 Download CSV File",
                                    data=content,
                                    file_name=f"test_cases_{project['name']}.csv",
                                    mime="text/csv"
                                )
                                st.success("✅ Export ready for download!")
                
                # Display test cases
                for tc in test_cases:
                    with st.expander(f"{tc['test_case_id']}: {tc['title']}"):
                        if tc.get('description'):
                            st.write(f"**Description:** {tc['description']}")
                        if tc.get('preconditions'):
                            st.write(f"**Preconditions:** {tc['preconditions']}")
                        if tc.get('test_steps'):
                            st.write(f"**Test Steps:** {tc['test_steps']}")
                        if tc.get('expected_result'):
                            st.write(f"**Expected Result:** {tc['expected_result']}")
                        st.write(f"**Type:** {tc.get('test_type', 'N/A')}")
                        st.write(f"**Priority:** {tc.get('priority', 'N/A')}")
            else:
                st.info("No test cases found. Generate some test cases first!")
    
    else:
        st.info("👈 Please select or create a project from the sidebar to get started.")
