"""
Test Case schemas for API request/response models.
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, field_validator
from ..models.test_case import TestCaseType, TestCasePriority


class TestCaseBase(BaseModel):
    """Base test case schema."""
    test_case_id: str
    title: str
    description: Optional[str] = None
    preconditions: Optional[str] = None
    test_steps: Optional[str] = None
    expected_result: Optional[str] = None
    test_type: TestCaseType = TestCaseType.FUNCTIONAL
    priority: TestCasePriority = TestCasePriority.MEDIUM
    
    @field_validator('title')
    @classmethod
    def validate_title(cls, v):
        if len(v.strip()) < 1:
            raise ValueError('Test case title cannot be empty')
        if len(v) > 500:
            raise ValueError('Test case title cannot exceed 500 characters')
        return v.strip()


class TestCaseCreate(TestCaseBase):
    """Schema for test case creation."""
    requirement_id: Optional[int] = None


class TestCaseUpdate(BaseModel):
    """Schema for test case updates."""
    title: Optional[str] = None
    description: Optional[str] = None
    preconditions: Optional[str] = None
    test_steps: Optional[str] = None
    expected_result: Optional[str] = None
    test_type: Optional[TestCaseType] = None
    priority: Optional[TestCasePriority] = None


class TestCaseInDB(TestCaseBase):
    """Schema for test case in database."""
    id: int
    project_id: int
    requirement_id: Optional[int] = None
    created_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class TestCase(TestCaseInDB):
    """Schema for test case response."""
    pass


class TestCaseGeneration(BaseModel):
    """Schema for test case generation request."""
    project_id: int
    document_id: Optional[int] = None
    query: Optional[str] = None
    num_test_cases: int = 5
    
    @field_validator('num_test_cases')
    @classmethod
    def validate_num_test_cases(cls, v):
        if v < 1 or v > 20:
            raise ValueError('Number of test cases must be between 1 and 20')
        return v


class TestCaseGenerationResponse(BaseModel):
    """Schema for test case generation response."""
    message: str
    project_id: int
    generated_count: int
    test_cases: List[TestCase]


class TestCaseExport(BaseModel):
    """Schema for test case export."""
    format: str = "markdown"  # markdown, json, csv
    project_id: int
    test_case_ids: Optional[List[int]] = None
