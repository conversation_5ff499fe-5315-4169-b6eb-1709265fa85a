"""
Project management API endpoints.
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...schemas import Project, ProjectCreate, ProjectUpdate, Document, Requirement
from ...models import Project as ProjectModel
from ...services import DocumentService
from ...api.v1.auth import get_current_active_user
from ...utils.logger import logger

router = APIRouter()


@router.post("/", response_model=Project, status_code=status.HTTP_201_CREATED)
async def create_project(
    project_data: ProjectCreate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Create a new project.
    
    Args:
        project_data: Project creation data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Created project
    """
    project = ProjectModel(
        name=project_data.name,
        description=project_data.description,
        owner_id=current_user.id
    )
    
    db.add(project)
    db.commit()
    db.refresh(project)
    
    logger.info(f"Project created: {project.name} by user {current_user.username}")
    return project


@router.get("/", response_model=List[Project])
async def get_user_projects(
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all projects for the current user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of user's projects
    """
    projects = db.query(ProjectModel).filter(ProjectModel.owner_id == current_user.id).all()
    return projects


@router.get("/{project_id}", response_model=Project)
async def get_project(
    project_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific project.
    
    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Project details
    """
    project = db.query(ProjectModel).filter(
        ProjectModel.id == project_id,
        ProjectModel.owner_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    return project


@router.put("/{project_id}", response_model=Project)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update a project.
    
    Args:
        project_id: Project ID
        project_update: Project update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated project
    """
    project = db.query(ProjectModel).filter(
        ProjectModel.id == project_id,
        ProjectModel.owner_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    update_data = project_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)
    
    db.commit()
    db.refresh(project)
    
    logger.info(f"Project updated: {project.name} by user {current_user.username}")
    return project


@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a project.
    
    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Success message
    """
    project = db.query(ProjectModel).filter(
        ProjectModel.id == project_id,
        ProjectModel.owner_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    db.delete(project)
    db.commit()
    
    logger.info(f"Project deleted: {project.name} by user {current_user.username}")
    return {"message": "Project deleted successfully"}


@router.post("/{project_id}/documents", response_model=Document, status_code=status.HTTP_201_CREATED)
async def upload_document(
    project_id: int,
    file: UploadFile = File(...),
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Upload a document to a project.
    
    Args:
        project_id: Project ID
        file: Uploaded file
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Uploaded document information
    """
    document = DocumentService.upload_document(db, file, project_id, current_user.id)
    logger.info(f"Document uploaded: {document.filename} to project {project_id}")
    return document


@router.get("/{project_id}/documents", response_model=List[Document])
async def get_project_documents(
    project_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get all documents for a project.
    
    Args:
        project_id: Project ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of project documents
    """
    documents = DocumentService.get_project_documents(db, project_id, current_user.id)
    return documents


@router.post("/{project_id}/documents/{document_id}/process", response_model=List[Requirement])
async def process_document(
    project_id: int,
    document_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Process a document to extract requirements.
    
    Args:
        project_id: Project ID
        document_id: Document ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List of extracted requirements
    """
    # Verify project ownership
    project = db.query(ProjectModel).filter(
        ProjectModel.id == project_id,
        ProjectModel.owner_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    requirements = DocumentService.process_document(db, document_id)
    logger.info(f"Document processed: {document_id}, found {len(requirements)} requirements")
    return requirements

@router.delete("/{project_id}/documents/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    project_id: int,
    document_id: int,
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a document and its file from the filesystem."""
    # Verify project ownership
    project = db.query(ProjectModel).filter(
        ProjectModel.id == project_id,
        ProjectModel.owner_id == current_user.id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Delete document using service
    DocumentService.delete_document(db, document_id, current_user.id)
    
    logger.info(f"Document {document_id} deleted by user {current_user.username}")
    return  # 204 No Content response
