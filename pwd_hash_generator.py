import streamlit_authenticator as stauth
import yaml

# Generate hashed passwords for version 0.2.3
def generate_config():
    # Your plain text passwords
    plain_passwords = {
        'sai': 'sai123',
        'srikanth': 'srikanth123',
        'admin': 'admin123'
    }
    
    # Hash the passwords
    hashed_passwords = {}
    for username, password in plain_passwords.items():
        hashed = stauth.Hasher([password]).generate()[0]
        hashed_passwords[username] = hashed
        print(f"Username: {username}, Password: {password}, Hashed: {hashed}")
    
    # Create config dictionary
    config = {
        'credentials': {
            'usernames': {
                'sai': {
                    'email': '<EMAIL>',
                    'name': '<PERSON>',
                    'password': hashed_passwords['sai']
                },
                'srikanth': {
                    'email': '<EMAIL>',
                    'name': 'Srikanth',
                    'password': hashed_passwords['srikanth']
                },
                'admin': {
                    'email': '<EMAIL>',
                    'name': 'Administrator',
                    'password': hashed_passwords['admin']
                }
            }
        },
        'cookie': {
            'name': 'my_auth_cookie',
            'key': '1234567890abcdef',  # Change this to a random string
            'expiry_days': 30
        },
        'preauthorized': {
            'emails': ['<EMAIL>']
        }
    }
    
    # Save to YAML file
    with open('config.yaml', 'w') as file:
        yaml.dump(config, file, default_flow_style=False)
    
    print("\n✅ config.yaml generated successfully!")
    print("\nYour config.yaml content:")
    print(yaml.dump(config, default_flow_style=False))

if __name__ == "__main__":
    generate_config()