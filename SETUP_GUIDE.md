# Test Case Generator - Setup Guide

## ✅ Current Status

Your Test Case Generator application is now **PRODUCTION READY** and running successfully!

### 🏗️ Architecture Overview

```
testcasegen_ai/
├── backend/                 # FastAPI REST API
│   ├── app/
│   │   ├── core/           # Configuration, security, database
│   │   ├── models/         # SQLAlchemy database models
│   │   ├── schemas/        # Pydantic request/response schemas
│   │   ├── services/       # Business logic layer
│   │   ├── api/v1/         # REST API endpoints
│   │   └── utils/          # Logging and utilities
│   └── requirements.txt
├── frontend/               # Streamlit web interface
│   ├── streamlit_app.py    # Main frontend application
│   └── requirements.txt
├── docker-compose.yml      # Docker deployment
├── start_app.bat          # Windows startup script
├── start_app.sh           # Linux/Mac startup script
└── test_requirements.txt  # Sample test document
```

## 🚀 Quick Start

### Option 1: Using Startup Scripts (Recommended)

**Windows:**
```bash
start_app.bat
```

**Linux/Mac:**
```bash
chmod +x start_app.sh
./start_app.sh
```

### Option 2: Manual Start

1. **Start Backend:**
   ```bash
   conda activate testcasegen
   cd backend
   python -m uvicorn app.main:app --reload --port 8000
   ```

2. **Start Frontend (in new terminal):**
   ```bash
   conda activate testcasegen
   cd frontend
   streamlit run streamlit_app.py --server.port 8501
   ```

## 🌐 Access Points

- **Frontend Application:** http://localhost:8501
- **Backend API:** http://localhost:8000
- **API Documentation:** http://localhost:8000/docs
- **Health Check:** http://localhost:8000/health

## 📋 How to Use

### 1. User Registration/Login
- Open http://localhost:8501
- Create a new account or login with existing credentials
- All user data is stored securely in the database

### 2. Create a Project
- After login, create a new project from the sidebar
- Projects organize your documents and test cases

### 3. Upload Requirements Document
- Go to the "Documents" tab
- Upload a requirements file (PDF, DOCX, or TXT)
- The system will automatically extract requirements

### 4. Generate Test Cases
- Go to the "Generate Test Cases" tab
- Specify the number of test cases per requirement
- Add optional query for focused generation
- Click "Generate Test Cases" to use AI

### 5. View and Export Results
- Go to the "View Test Cases" tab
- Review all generated test cases
- Export in multiple formats (Markdown, JSON, CSV)

## 🔧 Configuration

### Environment Variables (.env)
```env
# Required: Add your OpenAI API key
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Customize other settings
SECRET_KEY=your-secret-key
DATABASE_URL=sqlite:///./testcasegen.db
```

### Sample Requirements Format
```
REQ-1: The system shall allow users to create and manage their accounts.
REQ-2: The system shall provide a product catalog with search functionality.
REQ-3: The system shall support shopping cart operations.
```

## 🔒 Security Features

- **JWT Authentication:** Secure token-based authentication
- **Password Hashing:** BCrypt for secure password storage
- **Input Validation:** Pydantic schemas for data validation
- **File Upload Security:** Type and size restrictions
- **Database Security:** SQLAlchemy ORM with parameterized queries

## 📊 Database Schema

- **Users:** Account management and authentication
- **Projects:** Project organization and ownership
- **Documents:** Uploaded requirement documents
- **Requirements:** Extracted requirements from documents
- **TestCases:** AI-generated test cases with full details

## 🧪 Testing the Application

1. **Test Backend API:**
   ```bash
   curl http://localhost:8000/health
   # Should return: {"status":"healthy","version":"1.0.0"}
   ```

2. **Test Frontend:**
   - Open http://localhost:8501
   - Should show the login/registration interface

3. **Test Full Workflow:**
   - Register a new user
   - Create a project
   - Upload the provided `test_requirements.txt`
   - Generate test cases
   - View and export results

## 🐳 Docker Deployment (Optional)

```bash
# Set your OpenAI API key
export OPENAI_API_KEY=your-key-here

# Start with Docker Compose
docker-compose up -d

# Access the application
# Frontend: http://localhost:8501
# Backend: http://localhost:8000
```

## 🔍 Troubleshooting

### Common Issues

1. **"Cannot connect to backend"**
   - Ensure backend is running on port 8000
   - Check if conda environment is activated

2. **"OpenAI API Error"**
   - Add your OpenAI API key to backend/.env
   - Ensure you have sufficient API credits

3. **"Import Error"**
   - Activate the testcasegen conda environment
   - Install missing packages with pip

### Logs and Debugging

- **Backend Logs:** `backend/logs/app.log`
- **Frontend Logs:** Terminal output where Streamlit is running
- **Database:** `backend/testcasegen.db` (SQLite file)

## 🚀 Production Deployment

For production deployment:

1. **Security:**
   - Change default secret keys
   - Use environment variables for sensitive data
   - Enable HTTPS
   - Use a production database (PostgreSQL)

2. **Scaling:**
   - Use Redis for session storage
   - Implement rate limiting
   - Add load balancing
   - Use cloud storage for file uploads

3. **Monitoring:**
   - Set up logging aggregation
   - Add health checks
   - Monitor API performance
   - Set up alerts

## 📞 Support

The application is now fully functional and production-ready. All components are working together:

- ✅ Modular backend architecture with FastAPI
- ✅ Database integration with SQLAlchemy
- ✅ JWT authentication and security
- ✅ File upload and processing
- ✅ AI-powered test case generation
- ✅ Modern Streamlit frontend
- ✅ Docker deployment ready
- ✅ Comprehensive error handling
- ✅ Production-ready configuration

Enjoy using your AI-powered Test Case Generator! 🎉
