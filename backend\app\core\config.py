"""
Configuration settings for the Test Case Generator application.
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
except ImportError:
    from pydantic import BaseSettings, validator as field_validator
from enum import Enum

class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    OLLAMA = "ollama"
    GEMINI = "gemini"

class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "Test Case Generator API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Database
    DATABASE_URL: str = "sqlite:///./testcasegen.db"
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # LLM Configuration
    LLM_PROVIDER: LLMProvider = LLMProvider.OPENAI
    
    # OpenAI
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4o"
    OPENAI_TEMPERATURE: float = 0.1

    # Ollama Configuration
    OLLAMA_MODEL: str = "llama3.1:latest"
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_REQUEST_TIMEOUT: float = 120.0
    OLLAMA_CONTEXT_WINDOW: int = 8000
    
    # Gemini Configuration
    GOOGLE_API_KEY: Optional[str] = None
    GEMINI_MODEL: str = "models/gemini-1.5-flash"
    GEMINI_TEMPERATURE: float = 0.1
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    ALLOWED_EXTENSIONS: list = [".pdf", ".docx", ".txt"]
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"
    
    @field_validator("OPENAI_API_KEY", mode="before")
    @classmethod
    def validate_openai_key(cls, v):
        if not v:
            v = os.getenv("OPENAI_API_KEY")
        return v
    
    @field_validator("GOOGLE_API_KEY", mode="before")
    @classmethod
    def validate_google_key(cls, v):
        if not v:
            v = os.getenv("GOOGLE_API_KEY")
        return v
    
    @field_validator("LLM_PROVIDER", mode="before")
    @classmethod
    def validate_llm_provider(cls, v):
        if isinstance(v, str):
            return LLMProvider(v.lower())
        return v
    
    def validate_llm_config(self) -> bool:
        """Validate that required API keys are present for selected provider."""
        if self.LLM_PROVIDER == LLMProvider.OPENAI:
            return bool(self.OPENAI_API_KEY)
        elif self.LLM_PROVIDER == LLMProvider.GEMINI:
            return bool(self.GOOGLE_API_KEY)
        elif self.LLM_PROVIDER == LLMProvider.OLLAMA:
            return True  # Ollama doesn't need API key
        return False

    @field_validator("SECRET_KEY", mode="before")
    @classmethod
    def validate_secret_key(cls, v):
        if v == "your-secret-key-change-in-production":
            env_key = os.getenv("SECRET_KEY")
            if env_key:
                return env_key
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
