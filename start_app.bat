@echo off
echo Starting Test Case Generator Application...
echo.

echo Activating conda environment...
call conda activate testcasegen

echo.
echo Starting Backend API Server...
start "Backend API" cmd /k "cd backend && python -m uvicorn app.main:app --reload --port 8000"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting Frontend Streamlit App...
start "Frontend App" cmd /k "cd frontend && streamlit run streamlit_app.py --server.port 8501"

echo.
echo Application started successfully!
echo Backend API: http://localhost:8000
echo API Documentation: http://localhost:8000/docs
echo Frontend App: http://localhost:8501
echo.
echo Press any key to exit...
pause > nul
