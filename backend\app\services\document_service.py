"""
Document service for handling file uploads and processing.
"""
import os
import re
import shutil
from pathlib import Path
from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi import UploadFile, HTTPException, status

from ..models import Document, Requirement, DocumentStatus, Project
from ..core.config import settings
from ..utils.logger import logger


class DocumentService:
    """Service for handling document operations."""
    
    @staticmethod
    def upload_document(
        db: Session, 
        file: UploadFile, 
        project_id: int, 
        user_id: int
    ) -> Document:
        """
        Upload and save a document.
        
        Args:
            db: Database session
            file: Uploaded file
            project_id: Project ID
            user_id: User ID
            
        Returns:
            Created document
            
        Raises:
            HTTPException: If file validation fails
        """
        # Validate file
        DocumentService._validate_file(file)
        
        # Check if project exists and user has access
        project = db.query(Project).filter(
            Project.id == project_id,
            Project.owner_id == user_id
        ).first()
        
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        # Create upload directory
        upload_dir = Path(settings.UPLOAD_DIR) / str(user_id) / str(project_id)
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate unique filename
        file_extension = Path(file.filename).suffix
        filename = f"{len(os.listdir(upload_dir)) + 1}_{file.filename}"
        file_path = upload_dir / filename
        
        # Save file
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
        except Exception as e:
            logger.error(f"Error saving file: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error saving file"
            )
        
        # Create document record
        document = Document(
            filename=filename,
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=file_path.stat().st_size,
            content_type=file.content_type,
            project_id=project_id,
            uploaded_by=user_id,
            status=DocumentStatus.UPLOADED
        )
        
        db.add(document)
        db.commit()
        db.refresh(document)
        
        logger.info(f"Document uploaded: {filename} for project {project_id}")
        return document
    
    @staticmethod
    def process_document(db: Session, document_id: int) -> List[Requirement]:
        """
        Process a document to extract requirements.
        
        Args:
            db: Database session
            document_id: Document ID
            
        Returns:
            List of extracted requirements
        """
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        # Update status to processing
        document.status = DocumentStatus.PROCESSING
        db.commit()
        
        try:
            # Read file content
            content = DocumentService._read_file_content(document.file_path)
            
            # Extract requirements
            requirements = DocumentService._extract_requirements(content)
            
            # Save requirements to database
            db_requirements = []
            for req_id, description in requirements:
                requirement = Requirement(
                    requirement_id=req_id,
                    description=description,
                    document_id=document_id
                )
                db.add(requirement)
                db_requirements.append(requirement)
            
            # Update document status and content
            document.status = DocumentStatus.PROCESSED
            document.processed_content = content
            db.commit()
            
            logger.info(f"Processed document {document_id}: {len(requirements)} requirements found")
            return db_requirements
            
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            document.status = DocumentStatus.ERROR
            db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error processing document"
            )
    
    @staticmethod
    def get_project_documents(db: Session, project_id: int, user_id: int) -> List[Document]:
        """
        Get all documents for a project.
        
        Args:
            db: Database session
            project_id: Project ID
            user_id: User ID
            
        Returns:
            List of documents
        """
        return db.query(Document).join(Project).filter(
            Document.project_id == project_id,
            Project.owner_id == user_id
        ).all()
    
    @staticmethod
    def _validate_file(file: UploadFile) -> None:
        """
        Validate uploaded file.
        
        Args:
            file: Uploaded file
            
        Raises:
            HTTPException: If validation fails
        """
        # Check file size
        if hasattr(file, 'size') and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes"
            )
        
        # Check file extension
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_EXTENSIONS)}"
            )
    
    @staticmethod
    def _read_file_content(file_path: str) -> str:
        """
        Read content from file.
        
        Args:
            file_path: Path to file
            
        Returns:
            File content as string
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
    
    @staticmethod
    def _extract_requirements(content: str) -> List[tuple]:
        """
        Extract requirements from document content.
        
        Args:
            content: Document content
            
        Returns:
            List of (requirement_id, description) tuples
        """
        # Pattern to match requirements like "REQ-1: Description"
        pattern = r'REQ-(\d+):\s*(.*?)(?=\nREQ-\d+:|\Z)'
        matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
        
        requirements = []
        for req_num, description in matches:
            req_id = f"REQ-{req_num}"
            description = description.strip()
            requirements.append((req_id, description))
        
        return requirements
