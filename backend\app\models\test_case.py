"""
Test Case model for the Test Case Generator application.
"""
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, ForeignKey, Text, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from ..core.database import Base


class TestCaseType(enum.Enum):
    """Test case types."""
    FUNCTIONAL = "functional"
    NON_FUNCTIONAL = "non_functional"
    INTEGRATION = "integration"
    UNIT = "unit"
    ACCEPTANCE = "acceptance"


class TestCasePriority(enum.Enum):
    """Test case priorities."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class TestCase(Base):
    """Test Case model for storing generated test cases."""
    
    __tablename__ = "test_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(String(50), nullable=False, index=True)  # e.g., TC1.1
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    preconditions = Column(Text, nullable=True)
    test_steps = Column(Text, nullable=True)
    expected_result = Column(Text, nullable=True)
    test_type = Column(Enum(TestCaseType), default=TestCaseType.FUNCTIONAL)
    priority = Column(Enum(TestCasePriority), default=TestCasePriority.MEDIUM)
    
    # Foreign Keys
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    project = relationship("Project", back_populates="test_cases")
    requirement = relationship("Requirement", back_populates="test_cases")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<TestCase(id={self.id}, test_case_id='{self.test_case_id}', title='{self.title[:50]}...')>"
