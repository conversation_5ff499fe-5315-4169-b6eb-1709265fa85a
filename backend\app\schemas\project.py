"""
Project schemas for API request/response models.
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, field_validator


class ProjectBase(BaseModel):
    """Base project schema."""
    name: str
    description: Optional[str] = None
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        if len(v.strip()) < 1:
            raise ValueError('Project name cannot be empty')
        if len(v) > 100:
            raise ValueError('Project name cannot exceed 100 characters')
        return v.strip()


class ProjectCreate(ProjectBase):
    """Schema for project creation."""
    pass


class ProjectUpdate(BaseModel):
    """Schema for project updates."""
    name: Optional[str] = None
    description: Optional[str] = None


class ProjectInDB(ProjectBase):
    """Schema for project in database."""
    id: int
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Project(ProjectInDB):
    """Schema for project response."""
    pass


class ProjectWithStats(Project):
    """Schema for project with statistics."""
    document_count: int = 0
    test_case_count: int = 0
    requirement_count: int = 0


class DocumentBase(BaseModel):
    """Base document schema."""
    filename: str
    original_filename: str
    file_size: int
    content_type: str


class DocumentInDB(DocumentBase):
    """Schema for document in database."""
    id: int
    file_path: str
    status: str
    project_id: int
    uploaded_by: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class Document(DocumentInDB):
    """Schema for document response."""
    pass


class RequirementBase(BaseModel):
    """Base requirement schema."""
    requirement_id: str
    description: str


class RequirementInDB(RequirementBase):
    """Schema for requirement in database."""
    id: int
    document_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True


class Requirement(RequirementInDB):
    """Schema for requirement response."""
    pass
