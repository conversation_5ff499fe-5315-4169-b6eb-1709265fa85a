# Test Case Generator - Production Ready

A modular, production-ready AI-powered test case generator with FastAPI backend and Streamlit frontend.

## Architecture

```
testcasegen_ai/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── core/           # Core configuration and security
│   │   ├── models/         # SQLAlchemy database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # Business logic services
│   │   ├── api/            # API endpoints
│   │   └── utils/          # Utilities and logging
│   ├── requirements.txt
│   ├── Dockerfile
│   └── .env.example
├── frontend/               # Streamlit frontend
│   ├── streamlit_app.py
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml
└── README.md
```

## Features

- **Modular Architecture**: Clean separation of concerns with FastAPI backend
- **Database Integration**: SQLAlchemy ORM with proper relationships
- **Authentication**: JWT-based authentication with secure password hashing
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation
- **File Upload**: Secure document upload and processing
- **AI Integration**: OpenAI GPT-4 for intelligent test case generation
- **Export Functionality**: Multiple export formats (Markdown, JSON, CSV)
- **Docker Support**: Full containerization with Docker Compose
- **Production Ready**: Proper logging, error handling, and configuration

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose (optional)
- OpenAI API key

### Environment Setup

1. **Clone and navigate to the project:**
   ```bash
   cd testcasegen_ai
   ```

2. **Set up the backend:**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. **Install dependencies:**
   ```bash
   # Backend
   cd backend
   pip install -r requirements.txt
   
   # Frontend
   cd ../frontend
   pip install -r requirements.txt
   ```

### Running with Docker (Recommended)

1. **Set environment variables:**
   ```bash
   export OPENAI_API_KEY=your-openai-api-key-here
   ```

2. **Start the application:**
   ```bash
   docker-compose up -d
   ```

3. **Access the application:**
   - Frontend: http://localhost:8501
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Running Manually

1. **Start the backend:**
   ```bash
   cd backend
   uvicorn app.main:app --reload --port 8000
   ```

2. **Start the frontend (in another terminal):**
   ```bash
   cd frontend
   streamlit run streamlit_app.py --server.port 8501
   ```

## Configuration

### Backend Configuration (.env)

```env
# Application
APP_NAME=Test Case Generator API
DEBUG=false

# Database
DATABASE_URL=sqlite:///./testcasegen.db

# Security
SECRET_KEY=your-secret-key-here-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/auth/me` - Get current user info

### Projects
- `GET /api/v1/projects/` - Get user projects
- `POST /api/v1/projects/` - Create new project
- `POST /api/v1/projects/{id}/documents` - Upload document
- `POST /api/v1/projects/{id}/documents/{doc_id}/process` - Process document

### Test Cases
- `POST /api/v1/test-cases/generate` - Generate test cases
- `GET /api/v1/test-cases/project/{id}` - Get project test cases
- `POST /api/v1/test-cases/export` - Export test cases

## Usage

1. **Register/Login**: Create an account or login
2. **Create Project**: Set up a new project for your test cases
3. **Upload Document**: Upload requirements document (PDF, DOCX, TXT)
4. **Process Document**: Extract requirements from the document
5. **Generate Test Cases**: Use AI to generate comprehensive test cases
6. **Export Results**: Download test cases in various formats

## Database Schema

The application uses SQLAlchemy with the following main entities:

- **Users**: User accounts and authentication
- **Projects**: Project containers for organizing work
- **Documents**: Uploaded requirement documents
- **Requirements**: Extracted requirements from documents
- **TestCases**: Generated test cases linked to requirements

## Development

### Adding New Features

1. **Models**: Add new SQLAlchemy models in `backend/app/models/`
2. **Schemas**: Define Pydantic schemas in `backend/app/schemas/`
3. **Services**: Implement business logic in `backend/app/services/`
4. **APIs**: Create endpoints in `backend/app/api/v1/`

### Testing

```bash
# Backend tests
cd backend
pytest

# Frontend testing
cd frontend
streamlit run streamlit_app.py
```

## Production Deployment

### Security Considerations

1. Change default secret keys
2. Use environment variables for sensitive data
3. Enable HTTPS in production
4. Configure CORS appropriately
5. Use a production database (PostgreSQL recommended)

### Scaling

- Use Redis for session storage
- Implement rate limiting
- Add load balancing
- Use cloud storage for file uploads
- Monitor with logging and metrics

## Troubleshooting

### Common Issues

1. **API Connection Error**: Ensure backend is running on port 8000
2. **Authentication Issues**: Check JWT token expiration
3. **File Upload Fails**: Verify file size and type restrictions
4. **OpenAI Errors**: Validate API key and model availability

### Logs

- Backend logs: `backend/logs/app.log`
- Frontend logs: Streamlit console output

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please check the logs and API documentation at `/docs` endpoint.
