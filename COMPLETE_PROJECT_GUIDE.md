# 📚 Complete Test Case Generator Project Guide
## From Novice to Expert: Understanding Every Line of Code

---

## 📖 Table of Contents

1. [Project Overview & Architecture](#project-overview--architecture)
2. [Technology Stack Explained](#technology-stack-explained)
3. [Project Structure Deep Dive](#project-structure-deep-dive)
4. [Backend Architecture Explained](#backend-architecture-explained)
5. [Database Design & Models](#database-design--models)
6. [API Design & Endpoints](#api-design--endpoints)
7. [Frontend Architecture](#frontend-architecture)
8. [Security Implementation](#security-implementation)
9. [File Processing & AI Integration](#file-processing--ai-integration)
10. [Configuration & Environment](#configuration--environment)
11. [Deployment & Production](#deployment--production)
12. [Complete Code Walkthrough](#complete-code-walkthrough)
13. [How to Rebuild This Project](#how-to-rebuild-this-project)

---

## 1. Project Overview & Architecture

### 🎯 What This Project Does

This is an **AI-Powered Test Case Generator** that:
- Takes requirement documents (PDF, DOCX, TXT)
- Extracts individual requirements using regex patterns
- Uses OpenAI's GPT-4 to generate comprehensive test cases
- Provides a web interface for users to manage projects and test cases
- Exports results in multiple formats (Markdown, JSON, CSV)

### 🏗️ High-Level Architecture

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐
│   Streamlit     │ ◄─────────────────► │   FastAPI       │
│   Frontend      │                     │   Backend       │
│   (Port 8501)   │                     │   (Port 8000)   │
└─────────────────┘                     └─────────────────┘
                                                │
                                                ▼
                                        ┌─────────────────┐
                                        │   SQLite        │
                                        │   Database      │
                                        └─────────────────┘
                                                │
                                                ▼
                                        ┌─────────────────┐
                                        │   OpenAI API    │
                                        │   (GPT-4)       │
                                        └─────────────────┘
```

### 🔄 Data Flow

1. **User Registration/Login** → JWT Token Generation
2. **Project Creation** → Database Storage
3. **Document Upload** → File System Storage
4. **Document Processing** → Requirement Extraction
5. **AI Generation** → OpenAI API Call → Test Case Creation
6. **Export** → Format Conversion → File Download

---

## 2. Technology Stack Explained

### 🐍 Backend Technologies

**FastAPI** - Modern Python web framework
- **Why FastAPI?** 
  - Automatic API documentation (Swagger/OpenAPI)
  - Built-in data validation with Pydantic
  - High performance (comparable to Node.js)
  - Type hints support
  - Async/await support

**SQLAlchemy** - Python SQL toolkit and ORM
- **What is ORM?** Object-Relational Mapping
  - Maps database tables to Python classes
  - Handles SQL queries automatically
  - Provides database abstraction

**Pydantic** - Data validation using Python type annotations
- **Purpose:** Validates incoming/outgoing data
- **Benefits:** Automatic error messages, type conversion

**JWT (JSON Web Tokens)** - Stateless authentication
- **How it works:** Server creates signed token → Client stores token → Client sends token with requests

### 🎨 Frontend Technologies

**Streamlit** - Python web app framework
- **Why Streamlit?** 
  - Pure Python (no HTML/CSS/JavaScript needed)
  - Rapid prototyping
  - Built-in widgets and components
  - Real-time updates

### 🤖 AI Integration

**OpenAI GPT-4** - Large Language Model
- **Purpose:** Generate intelligent test cases from requirements
- **API Integration:** REST API calls with structured prompts

### 🗄️ Database

**SQLite** - Lightweight database
- **Why SQLite?** 
  - No server setup required
  - Perfect for development and small applications
  - Can be easily migrated to PostgreSQL for production

---

## 3. Project Structure Deep Dive

```
testcasegen_ai/
├── backend/                    # Backend API server
│   ├── app/                   # Main application package
│   │   ├── __init__.py       # Makes 'app' a Python package
│   │   ├── main.py           # FastAPI application entry point
│   │   ├── core/             # Core functionality (config, security, database)
│   │   │   ├── __init__.py   # Package initialization
│   │   │   ├── config.py     # Application configuration settings
│   │   │   ├── database.py   # Database connection and session management
│   │   │   └── security.py   # Authentication and security utilities
│   │   ├── models/           # Database models (SQLAlchemy)
│   │   │   ├── __init__.py   # Package initialization
│   │   │   ├── user.py       # User database model
│   │   │   ├── project.py    # Project database model
│   │   │   ├── document.py   # Document and Requirement models
│   │   │   └── test_case.py  # Test case database model
│   │   ├── schemas/          # API request/response models (Pydantic)
│   │   │   ├── __init__.py   # Package initialization
│   │   │   ├── user.py       # User API schemas
│   │   │   ├── project.py    # Project API schemas
│   │   │   └── test_case.py  # Test case API schemas
│   │   ├── services/         # Business logic layer
│   │   │   ├── __init__.py   # Package initialization
│   │   │   ├── auth_service.py      # User authentication logic
│   │   │   ├── document_service.py  # Document processing logic
│   │   │   └── test_case_service.py # Test case generation logic
│   │   ├── api/              # API endpoints
│   │   │   ├── __init__.py   # Package initialization
│   │   │   └── v1/           # API version 1
│   │   │       ├── __init__.py # Package initialization
│   │   │       ├── auth.py     # Authentication endpoints
│   │   │       ├── projects.py # Project management endpoints
│   │   │       └── test_cases.py # Test case endpoints
│   │   └── utils/            # Utility functions
│   │       ├── __init__.py   # Package initialization
│   │       └── logger.py     # Logging configuration
│   ├── requirements.txt      # Python dependencies
│   ├── Dockerfile           # Docker container configuration
│   └── .env                 # Environment variables
├── frontend/               # Frontend web application
│   ├── streamlit_app.py   # Main Streamlit application
│   ├── requirements.txt   # Frontend dependencies
│   └── Dockerfile         # Frontend Docker configuration
├── docker-compose.yml     # Multi-container Docker setup
├── README.md             # Project documentation
├── SETUP_GUIDE.md        # Setup instructions
└── test_requirements.txt # Sample test data
```

### 📁 Why This Structure?

**Separation of Concerns:** Each folder has a specific responsibility
- `models/` - Database structure
- `schemas/` - API data validation
- `services/` - Business logic
- `api/` - HTTP endpoints

**Scalability:** Easy to add new features without affecting existing code

**Maintainability:** Clear organization makes debugging easier

---

## 4. Backend Architecture Explained

### 🏛️ Layered Architecture Pattern

```
┌─────────────────────────────────────────────────────────┐
│                    API Layer                            │
│  (FastAPI routes - handles HTTP requests/responses)     │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                 Service Layer                           │
│     (Business logic - processes data)                   │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                 Model Layer                             │
│    (Database models - data structure)                   │
└─────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────┐
│                 Database Layer                          │
│        (SQLite - data storage)                          │
└─────────────────────────────────────────────────────────┘
```

### 🔄 Request Flow Example

1. **User makes API request** → `POST /api/v1/auth/login`
2. **FastAPI route** → `auth.py` receives request
3. **Pydantic validation** → Validates username/password format
4. **Service layer** → `auth_service.py` processes login logic
5. **Database query** → SQLAlchemy queries user table
6. **Response** → JWT token returned to user

---

## 5. Database Design & Models

### 🗄️ Database Schema

```sql
-- Users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Documents table
CREATE TABLE documents (
    id INTEGER PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'uploaded',
    processed_content TEXT,
    project_id INTEGER REFERENCES projects(id),
    uploaded_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

-- Requirements table
CREATE TABLE requirements (
    id INTEGER PRIMARY KEY,
    requirement_id VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    document_id INTEGER REFERENCES documents(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test cases table
CREATE TABLE test_cases (
    id INTEGER PRIMARY KEY,
    test_case_id VARCHAR(50) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    preconditions TEXT,
    test_steps TEXT,
    expected_result TEXT,
    test_type VARCHAR(20) DEFAULT 'functional',
    priority VARCHAR(20) DEFAULT 'medium',
    project_id INTEGER REFERENCES projects(id),
    requirement_id INTEGER REFERENCES requirements(id),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 🔗 Relationships Explained

**One-to-Many Relationships:**
- One User → Many Projects
- One Project → Many Documents
- One Document → Many Requirements
- One Requirement → Many Test Cases

**Foreign Keys:**
- `owner_id` in projects links to `users.id`
- `project_id` in documents links to `projects.id`
- `requirement_id` in test_cases links to `requirements.id`

---

## 6. API Design & Endpoints

### 🌐 RESTful API Design Principles

**REST** = Representational State Transfer
- **Resources:** Everything is a resource (users, projects, test cases)
- **HTTP Methods:** GET (read), POST (create), PUT (update), DELETE (delete)
- **Stateless:** Each request contains all needed information
- **JSON:** Data exchange format

### 📋 Complete API Endpoints

```
Authentication Endpoints:
POST   /api/v1/auth/register     # Create new user account
POST   /api/v1/auth/login        # User login (returns JWT token)
GET    /api/v1/auth/me           # Get current user info
POST   /api/v1/auth/refresh      # Refresh JWT token
POST   /api/v1/auth/logout       # Logout (client-side token removal)

Project Endpoints:
GET    /api/v1/projects/         # Get all user projects
POST   /api/v1/projects/         # Create new project
GET    /api/v1/projects/{id}     # Get specific project
PUT    /api/v1/projects/{id}     # Update project
DELETE /api/v1/projects/{id}     # Delete project

Document Endpoints:
POST   /api/v1/projects/{id}/documents              # Upload document
GET    /api/v1/projects/{id}/documents              # Get project documents
POST   /api/v1/projects/{id}/documents/{doc_id}/process  # Process document

Test Case Endpoints:
POST   /api/v1/test-cases/generate           # Generate test cases using AI
GET    /api/v1/test-cases/project/{id}       # Get project test cases
POST   /api/v1/test-cases/                   # Create manual test case
GET    /api/v1/test-cases/{id}               # Get specific test case
PUT    /api/v1/test-cases/{id}               # Update test case
DELETE /api/v1/test-cases/{id}               # Delete test case
POST   /api/v1/test-cases/export             # Export test cases

Health Check:
GET    /                                     # Root endpoint
GET    /health                               # Health check
GET    /docs                                 # API documentation (Swagger)
```

### 🔐 Authentication Flow

```
1. User Registration:
   POST /api/v1/auth/register
   Body: {"username": "john", "email": "<EMAIL>", "full_name": "John Doe", "password": "secret123"}
   Response: {"id": 1, "username": "john", "email": "<EMAIL>", ...}

2. User Login:
   POST /api/v1/auth/login
   Body: {"username": "john", "password": "secret123"}
   Response: {"access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...", "token_type": "bearer"}

3. Authenticated Requests:
   Headers: {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}
```

---

## 7. Complete Code Walkthrough

### 📄 backend/app/main.py - Application Entry Point

```python
"""
Main FastAPI application for the Test Case Generator.
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import our custom modules
from .core.config import settings
from .core.database import create_tables
from .utils.logger import setup_logging, logger
from .api.v1 import auth, projects, test_cases
```

**Line-by-Line Explanation:**

1. **Docstring:** Python documentation string explaining the file purpose
2. **Imports:**
   - `FastAPI` - Main web framework class
   - `HTTPException` - For handling HTTP errors
   - `CORSMiddleware` - Handles Cross-Origin Resource Sharing
   - `JSONResponse` - For JSON responses
   - `uvicorn` - ASGI server to run the application
3. **Custom imports:** Our application modules

```python
# Setup logging
setup_logging()

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-Powered Test Case Generator API",
    docs_url="/docs",
    redoc_url="/redoc"
)
```

**Explanation:**
- `setup_logging()` - Configures application logging
- `FastAPI()` - Creates the main application instance
- `title`, `version`, `description` - Metadata for API documentation
- `docs_url="/docs"` - Swagger UI documentation endpoint
- `redoc_url="/redoc"` - Alternative documentation format

```python
# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

**CORS Middleware Explanation:**
- **Purpose:** Allows frontend (port 8501) to call backend (port 8000)
- **allow_origins=["*"]** - Allows requests from any domain (development only!)
- **allow_credentials=True** - Allows cookies and authorization headers
- **allow_methods=["*"]** - Allows all HTTP methods (GET, POST, PUT, DELETE)
- **allow_headers=["*"]** - Allows all request headers

```python
# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(projects.router, prefix="/api/v1/projects", tags=["Projects"])
app.include_router(test_cases.router, prefix="/api/v1/test-cases", tags=["Test Cases"])
```

**Router Inclusion:**
- **Purpose:** Organizes endpoints into logical groups
- **prefix="/api/v1/auth"** - All auth endpoints start with this path
- **tags=["Authentication"]** - Groups endpoints in documentation

```python
@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    logger.info("Starting Test Case Generator API...")

    # Create database tables
    try:
        create_tables()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise

    logger.info("Application startup completed")
```

**Startup Event:**
- **@app.on_event("startup")** - Decorator that runs function when app starts
- **async def** - Asynchronous function (can handle multiple requests)
- **create_tables()** - Creates database tables if they don't exist
- **try/except** - Error handling for database creation

### 📄 backend/app/core/config.py - Configuration Management

```python
"""
Configuration settings for the Test Case Generator application.
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
    from pydantic import field_validator
except ImportError:
    from pydantic import BaseSettings, validator as field_validator
```

**Import Explanation:**
- **os** - Operating system interface for environment variables
- **typing.Optional** - Type hint for optional values
- **try/except** - Handles different Pydantic versions
- **BaseSettings** - Pydantic class for configuration management
- **field_validator** - Decorator for validating configuration values

```python
class Settings(BaseSettings):
    """Application settings."""

    # Application
    APP_NAME: str = "Test Case Generator API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False

    # Database
    DATABASE_URL: str = "sqlite:///./testcasegen.db"

    # Security
    SECRET_KEY: str = "HhkEbE1ndAUqwKTlfdytt9KpBJosZc9NVEbTohX4kDg"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
```

**Settings Class:**
- **BaseSettings** - Automatically loads from environment variables
- **Type hints** - `str`, `bool`, `int` specify expected data types
- **Default values** - Used if environment variable not set
- **DATABASE_URL** - SQLite database file location
- **SECRET_KEY** - Used for JWT token signing (MUST change in production!)
- **ALGORITHM** - JWT signing algorithm
- **ACCESS_TOKEN_EXPIRE_MINUTES** - How long tokens remain valid

```python
    @field_validator("OPENAI_API_KEY", mode="before")
    @classmethod
    def validate_openai_key(cls, v):
        if not v:
            v = os.getenv("OPENAI_API_KEY")
        return v
```

**Validator Explanation:**
- **@field_validator** - Pydantic decorator for custom validation
- **mode="before"** - Runs before Pydantic's built-in validation
- **@classmethod** - Method belongs to class, not instance
- **os.getenv()** - Gets environment variable value
- **Purpose:** Ensures OpenAI API key is loaded from environment

### 📄 backend/app/core/database.py - Database Connection

```python
"""
Database configuration and session management.
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from .config import settings
```

**SQLAlchemy Imports:**
- **create_engine** - Creates database connection
- **declarative_base** - Base class for database models
- **sessionmaker** - Factory for database sessions
- **Session** - Type hint for database sessions
- **Generator** - Type hint for yielding values

```python
# Create SQLAlchemy engine
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)
```

**Engine Creation:**
- **create_engine()** - Establishes database connection pool
- **settings.DATABASE_URL** - Database connection string
- **connect_args** - SQLite-specific configuration
- **check_same_thread": False** - Allows SQLite use across threads

```python
# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for models
Base = declarative_base()
```

**Session Configuration:**
- **sessionmaker** - Creates session factory
- **autocommit=False** - Manual transaction control
- **autoflush=False** - Manual flush control
- **bind=engine** - Connects sessions to database engine
- **Base** - Parent class for all database models

```python
def get_db() -> Generator[Session, None, None]:
    """
    Dependency to get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

**Database Dependency:**
- **Generator[Session, None, None]** - Type hint for generator
- **yield db** - Provides database session to request
- **try/finally** - Ensures database connection is always closed
- **Purpose:** FastAPI dependency injection for database access

### 📄 backend/app/models/user.py - User Database Model

```python
"""
User model for the Test Case Generator application.
"""
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base
```

**SQLAlchemy Imports:**
- **Column** - Defines database column
- **Integer, String, DateTime, Boolean** - Column data types
- **relationship** - Defines relationships between tables
- **func** - SQL functions (like NOW())
- **Base** - Parent class from database.py

```python
class User(Base):
    """User model for storing user information."""

    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
```

**Model Definition:**
- **class User(Base)** - Inherits from SQLAlchemy Base
- **__tablename__ = "users"** - Database table name
- **Column definitions:**
  - **primary_key=True** - Unique identifier
  - **index=True** - Creates database index for faster queries
  - **unique=True** - Ensures no duplicates
  - **nullable=False** - Required field
  - **default=True** - Default value when creating record
  - **server_default=func.now()** - Database sets timestamp
  - **onupdate=func.now()** - Updates timestamp on record change

```python
    # Relationships
    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
```

**Relationships:**
- **relationship("Project")** - Links to Project model
- **back_populates="owner"** - Bidirectional relationship
- **cascade="all, delete-orphan"** - Delete projects when user is deleted
- **__repr__()** - String representation for debugging

### 📄 backend/app/schemas/user.py - API Data Validation

```python
"""
User schemas for API request/response models.
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, field_validator
```

**Pydantic Imports:**
- **BaseModel** - Base class for data validation
- **EmailStr** - Validates email format
- **field_validator** - Custom validation decorator
- **Optional** - Field can be None
- **datetime** - Date/time data type

```python
class UserCreate(BaseModel):
    """Schema for user creation."""
    username: str
    email: EmailStr
    full_name: str
    password: str

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.isalnum():
            raise ValueError('Username must contain only alphanumeric characters')
        return v
```

**Schema Purpose:**
- **Validates incoming data** from API requests
- **Type checking** - Ensures correct data types
- **Custom validation** - Business rule enforcement
- **Error messages** - User-friendly validation errors

**Validator Logic:**
- **len(v) < 3** - Minimum length check
- **v.isalnum()** - Only letters and numbers allowed
- **raise ValueError()** - Stops processing with error message
- **return v** - Returns validated value

### 📄 backend/app/services/auth_service.py - Business Logic

```python
"""
Authentication service for user management.
"""
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from ..models import User
from ..schemas import UserCreate, UserUpdate
from ..core.security import get_password_hash, verify_password
from ..utils.logger import logger
```

**Service Layer Purpose:**
- **Separates business logic** from API endpoints
- **Reusable functions** across different parts of application
- **Database operations** with proper error handling
- **Security operations** like password hashing

```python
class AuthService:
    """Service for handling authentication and user management."""

    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        """
        Create a new user.

        Args:
            db: Database session
            user_create: User creation data

        Returns:
            Created user

        Raises:
            HTTPException: If username or email already exists
        """
```

**Method Documentation:**
- **@staticmethod** - Method doesn't need class instance
- **Type hints** - `db: Session`, `user_create: UserCreate`, `-> User`
- **Docstring** - Explains purpose, parameters, return value, exceptions
- **Args/Returns/Raises** - Standard documentation format

```python
        # Check if username already exists
        if AuthService.get_user_by_username(db, user_create.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
```

**Business Logic:**
- **Duplicate check** - Prevents duplicate usernames
- **HTTPException** - FastAPI exception with HTTP status code
- **status.HTTP_400_BAD_REQUEST** - Standard HTTP 400 error
- **detail** - Error message returned to client

```python
        # Create new user
        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=hashed_password
        )

        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        logger.info(f"Created new user: {user_create.username}")
        return db_user
```

**Database Operations:**
- **get_password_hash()** - Securely hashes password
- **User()** - Creates SQLAlchemy model instance
- **db.add()** - Adds to database session
- **db.commit()** - Saves changes to database
- **db.refresh()** - Reloads object with database-generated values
- **logger.info()** - Logs successful operation

---

## 8. Frontend Architecture - Streamlit Deep Dive

### 🎨 frontend/streamlit_app.py - Complete Walkthrough

```python
"""
Streamlit frontend for the Test Case Generator application.
This version uses the FastAPI backend for all operations.
"""
import streamlit as st
import requests
import json
from pathlib import Path
from typing import Optional, Dict, Any
```

**Import Explanation:**
- **streamlit as st** - Main Streamlit library (aliased for convenience)
- **requests** - HTTP client for API calls
- **json** - JSON data handling
- **pathlib.Path** - Modern file path handling
- **typing** - Type hints for better code documentation

```python
# Configuration
API_BASE_URL = "http://localhost:8000/api/v1"

# Page config
st.set_page_config(
    page_title="Test Case Generator",
    page_icon="🔧",
    layout="wide"
)
```

**Streamlit Configuration:**
- **API_BASE_URL** - Backend server address
- **st.set_page_config()** - Must be first Streamlit command
- **page_title** - Browser tab title
- **page_icon** - Browser tab icon (emoji)
- **layout="wide"** - Uses full browser width

```python
# Session state initialization
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_info' not in st.session_state:
    st.session_state.user_info = None
if 'current_project' not in st.session_state:
    st.session_state.current_project = None
```

**Session State:**
- **st.session_state** - Streamlit's way to store data between page reloads
- **access_token** - JWT token for authentication
- **user_info** - Current user's information
- **current_project** - Currently selected project
- **Purpose:** Maintains user state across interactions

### 🔧 API Helper Functions

```python
def make_api_request(
    method: str,
    endpoint: str,
    data: Optional[Dict] = None,
    files: Optional[Dict] = None,
    headers: Optional[Dict] = None
) -> Optional[Dict[str, Any]]:
    """Make API request with error handling."""
    url = f"{API_BASE_URL}{endpoint}"

    # Add authorization header if token exists
    if st.session_state.access_token and headers is None:
        headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
    elif st.session_state.access_token and headers:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"
```

**Function Purpose:**
- **Centralized API communication** - All backend calls go through this function
- **Automatic authentication** - Adds JWT token to requests
- **Error handling** - Consistent error management
- **Type hints** - Clear parameter and return types

**Parameter Explanation:**
- **method: str** - HTTP method (GET, POST, PUT, DELETE)
- **endpoint: str** - API endpoint path
- **data: Optional[Dict]** - JSON data for request body
- **files: Optional[Dict]** - File uploads
- **headers: Optional[Dict]** - Custom HTTP headers

```python
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            if files:
                response = requests.post(url, data=data, files=files, headers=headers)
            else:
                response = requests.post(url, json=data, headers=headers)
        # ... other methods

        if response.status_code == 200 or response.status_code == 201:
            return response.json()
        elif response.status_code == 401:
            st.error("Authentication failed. Please login again.")
            logout_user()
            return None
        else:
            error_detail = response.json().get("detail", "Unknown error")
            st.error(f"API Error: {error_detail}")
            return None

    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to the backend server. Please ensure the API server is running on http://localhost:8000")
        return None
    except Exception as e:
        st.error(f"Request failed: {str(e)}")
        return None
```

**Error Handling Strategy:**
- **HTTP Status Codes:**
  - **200/201** - Success, return JSON data
  - **401** - Unauthorized, logout user
  - **Other** - Show error message
- **Connection Errors** - Backend server not running
- **General Exceptions** - Catch-all error handling

### 🔐 Authentication Functions

```python
def login_user(username: str, password: str) -> bool:
    """Login user and store token."""
    data = {"username": username, "password": password}
    response = make_api_request("POST", "/auth/login", data=data, headers={})

    if response:
        st.session_state.access_token = response["access_token"]
        # Get user info
        user_info = make_api_request("GET", "/auth/me")
        if user_info:
            st.session_state.user_info = user_info
            return True

    return False
```

**Login Flow:**
1. **Send credentials** to backend `/auth/login` endpoint
2. **Receive JWT token** in response
3. **Store token** in session state
4. **Get user info** using token
5. **Return success/failure** boolean

### 🎨 User Interface Components

```python
# Custom CSS
st.markdown("""
    <style>
        .main-title {
            font-size: 3rem;
            font-weight: 700;
            color: #4F8BF9;
        }
        .sub-title {
            font-size: 1.2rem;
            color: #444;
        }
        .stButton>button {
            border-radius: 10px;
            background-color: #4F8BF9;
            color: white;
            font-weight: bold;
            padding: 10px 20px;
        }
    </style>
""", unsafe_allow_html=True)
```

**CSS Styling:**
- **st.markdown()** - Renders HTML/CSS
- **unsafe_allow_html=True** - Allows HTML content
- **Custom classes** - Styles for titles and buttons
- **Purpose:** Makes the app look professional

### 🔄 Main Application Logic

```python
if not st.session_state.access_token:
    # Authentication UI
    st.markdown("<p class='main-title'>🔧 Test Case Generator</p>", unsafe_allow_html=True)
    st.markdown("---")

    tab1, tab2 = st.tabs(["🔐 Login", "📝 Create Account"])
```

**Conditional Rendering:**
- **if not st.session_state.access_token** - Show login if not authenticated
- **st.tabs()** - Creates tabbed interface
- **Separation of concerns** - Login and registration in separate tabs

```python
    with tab1:
        st.subheader("🔐 Login to Your Account")

        with st.form("login_form"):
            username = st.text_input("Username")
            password = st.text_input("Password", type="password")
            login_button = st.form_submit_button("🔑 Login")

            if login_button:
                if username and password:
                    if login_user(username, password):
                        st.success("✅ Login successful!")
                        st.rerun()
                    else:
                        st.error("❌ Invalid username or password")
                else:
                    st.warning("⚠️ Please enter both username and password")
```

**Form Handling:**
- **st.form()** - Groups inputs together
- **st.text_input()** - Text input field
- **type="password"** - Hides password input
- **st.form_submit_button()** - Form submission
- **st.rerun()** - Refreshes the page after login
- **Validation** - Checks if fields are filled

---

## 9. Security Implementation Deep Dive

### 🔐 backend/app/core/security.py - Security Functions

```python
"""
Security utilities for authentication and authorization.
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

from .config import settings
```

**Security Libraries:**
- **jose** - JSON Web Token implementation
- **passlib** - Password hashing library
- **HTTPBearer** - FastAPI security scheme for Bearer tokens
- **datetime/timedelta** - Token expiration handling

```python
# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT token security
security = HTTPBearer()
```

**Security Setup:**
- **CryptContext** - Configures password hashing
- **schemes=["bcrypt"]** - Uses bcrypt algorithm (industry standard)
- **deprecated="auto"** - Automatically handles algorithm upgrades
- **HTTPBearer()** - Extracts Bearer tokens from Authorization header

```python
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.

    Args:
        data: Data to encode in the token
        expires_delta: Token expiration time

    Returns:
        Encoded JWT token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt
```

**JWT Token Creation:**
- **data.copy()** - Avoids modifying original data
- **expires_delta** - Custom expiration time (optional)
- **datetime.utcnow()** - Current UTC time
- **"exp"** - Standard JWT expiration claim
- **jwt.encode()** - Creates signed token
- **SECRET_KEY** - Used for signing (must be secret!)
- **ALGORITHM** - Signing algorithm (HS256)

```python
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.

    Args:
        plain_password: Plain text password
        hashed_password: Hashed password

    Returns:
        True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)
```

**Password Verification:**
- **pwd_context.verify()** - Compares plain text with hash
- **Secure comparison** - Uses constant-time comparison
- **No password storage** - Only hashes are stored in database

### 🛡️ Security Best Practices Implemented

1. **Password Hashing:**
   - Never store plain text passwords
   - Use bcrypt with automatic salt generation
   - Resistant to rainbow table attacks

2. **JWT Tokens:**
   - Stateless authentication
   - Signed with secret key
   - Include expiration time
   - Can be revoked by changing secret key

3. **Input Validation:**
   - Pydantic schemas validate all input
   - Type checking prevents injection attacks
   - Custom validators enforce business rules

4. **Error Handling:**
   - Don't reveal sensitive information in errors
   - Generic error messages for security failures
   - Proper HTTP status codes

---

## 10. AI Integration - OpenAI GPT-4

### 🤖 backend/app/services/test_case_service.py - AI Logic

```python
from llama_index.llms.openai import OpenAI

class TestCaseService:
    """Service for handling test case operations."""

    def __init__(self):
        """Initialize the test case service."""
        self.llm = OpenAI(
            temperature=settings.OPENAI_TEMPERATURE,
            model=settings.OPENAI_MODEL,
            api_key=settings.OPENAI_API_KEY
        )
```

**AI Service Setup:**
- **llama_index** - Framework for LLM applications
- **OpenAI class** - Wrapper for OpenAI API
- **temperature** - Controls randomness (0.1 = more focused)
- **model** - GPT model to use (gpt-4o)
- **api_key** - Authentication for OpenAI API

```python
def _generate_test_cases_for_requirement(self, requirement: Requirement, num_cases: int) -> List[dict]:
    """Generate test cases for a specific requirement using LLM."""
    prompt = f"""
You are a senior QA engineer tasked with creating test cases for a software system.
Generate {num_cases} test cases for the following requirement:

REQUIREMENT ID: {requirement.requirement_id}
DESCRIPTION: {requirement.description}

For each test case, provide:
1. Test Case ID (format: TC{requirement.requirement_id.split('-')[1]}.X, e.g., TC1.1, TC1.2)
2. Test Case Title (descriptive, concise, starting with 'Verify', 'Confirm', or 'Test')
3. Description (brief explanation of what the test validates)
4. Preconditions (what must be true before executing the test)
5. Test Steps (numbered steps to execute the test)
6. Expected Result (what should happen when the test passes)

Focus on:
- Positive test scenarios (valid inputs, expected behavior)
- Negative test scenarios (invalid inputs, error handling)
- Edge cases (boundary values, extreme conditions)

Format your response as JSON:
{{
    "test_cases": [
        {{
            "id": "TC{requirement.requirement_id.split('-')[1]}.1",
            "title": "Verify that a new user can successfully register with valid information",
            "description": "Test the user registration functionality with valid input data",
            "preconditions": "User is not already registered",
            "test_steps": "1. Navigate to registration page\\n2. Enter valid user details\\n3. Click register button",
            "expected_result": "User is successfully registered and redirected to login page"
        }}
    ]
}}
"""
```

**Prompt Engineering:**
- **Role definition** - "You are a senior QA engineer"
- **Clear instructions** - Specific format and requirements
- **Examples** - Shows expected output format
- **JSON structure** - Ensures parseable response
- **Test case types** - Positive, negative, edge cases

**Why This Approach Works:**
1. **Structured prompt** - Clear expectations for AI
2. **JSON format** - Easy to parse programmatically
3. **Professional context** - AI understands QA perspective
4. **Specific requirements** - Reduces ambiguous responses

```python
try:
    response = self.llm.complete(prompt)
    return self._parse_test_case_response(response.text, requirement)
except Exception as e:
    logger.error(f"Error calling LLM for requirement {requirement.requirement_id}: {e}")
    raise
```

**AI API Call:**
- **self.llm.complete()** - Sends prompt to OpenAI
- **response.text** - Gets AI-generated text
- **Error handling** - Logs failures for debugging
- **Fallback** - Creates basic test case if AI fails

---

## 11. File Processing & Document Handling

### 📄 backend/app/services/document_service.py - File Processing

```python
def upload_document(
    db: Session,
    file: UploadFile,
    project_id: int,
    user_id: int
) -> Document:
    """
    Upload and save a document.
    """
    # Validate file
    DocumentService._validate_file(file)

    # Create upload directory
    upload_dir = Path(settings.UPLOAD_DIR) / str(user_id) / str(project_id)
    upload_dir.mkdir(parents=True, exist_ok=True)

    # Generate unique filename
    file_extension = Path(file.filename).suffix
    filename = f"{len(os.listdir(upload_dir)) + 1}_{file.filename}"
    file_path = upload_dir / filename

    # Save file
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
```

**File Upload Process:**
1. **Validate file** - Check size and type
2. **Create directory structure** - `uploads/user_id/project_id/`
3. **Generate unique filename** - Prevents conflicts
4. **Save to filesystem** - Binary file copy
5. **Create database record** - Track file metadata

**Security Considerations:**
- **File type validation** - Only allow PDF, DOCX, TXT
- **Size limits** - Prevent large file uploads
- **User isolation** - Files stored per user/project
- **Filename sanitization** - Prevent directory traversal

```python
def _extract_requirements(content: str) -> List[tuple]:
    """
    Extract requirements from document content.
    """
    # Pattern to match requirements like "REQ-1: Description"
    pattern = r'REQ-(\d+):\s*(.*?)(?=\nREQ-\d+:|\Z)'
    matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)

    requirements = []
    for req_num, description in matches:
        req_id = f"REQ-{req_num}"
        description = description.strip()
        requirements.append((req_id, description))

    return requirements
```

**Requirement Extraction:**
- **Regex pattern** - Matches "REQ-1: Description" format
- **re.MULTILINE** - Handles multi-line text
- **re.DOTALL** - Dot matches newlines
- **Lookahead** - `(?=\nREQ-\d+:|\Z)` stops at next requirement or end
- **Tuple return** - (requirement_id, description) pairs

### 📊 Export Functionality Deep Dive

```python
def _export_to_markdown(self, test_cases: List[TestCase]) -> str:
    """Export test cases to markdown format grouped by requirements."""
    content = "# Test Cases Report\n\n"

    # Group test cases by requirement
    requirements_map = {}
    for tc in test_cases:
        if tc.requirement:
            req_id = tc.requirement.requirement_id
            if req_id not in requirements_map:
                requirements_map[req_id] = {
                    'requirement': tc.requirement,
                    'test_cases': []
                }
            requirements_map[req_id]['test_cases'].append(tc)
```

**Export Strategy:**
1. **Group by requirement** - Organize test cases under their requirements
2. **Requirement first** - Show requirement description before test cases
3. **Complete traceability** - Link test cases to requirements
4. **Multiple formats** - Markdown, JSON, CSV support

**Why This Approach:**
- **Better readability** - Requirements provide context
- **Traceability** - Easy to see which test cases cover which requirements
- **Self-contained** - No need to reference separate files
- **Professional format** - Suitable for documentation

---

## 12. Complete Step-by-Step Rebuild Guide

### 🏗️ How to Rebuild This Project from Scratch

#### Step 1: Environment Setup

```bash
# Create conda environment
conda create -n testcasegen python=3.11
conda activate testcasegen

# Create project directory
mkdir testcasegen_ai
cd testcasegen_ai
```

#### Step 2: Backend Structure Creation

```bash
# Create backend directory structure
mkdir -p backend/app/core
mkdir -p backend/app/models
mkdir -p backend/app/schemas
mkdir -p backend/app/services
mkdir -p backend/app/api/v1
mkdir -p backend/app/utils
```

#### Step 3: Install Dependencies

```bash
# Backend dependencies
pip install fastapi uvicorn sqlalchemy pydantic-settings
pip install python-jose passlib python-multipart email-validator
pip install llama-index llama-index-llms-openai openai
```

#### Step 4: Core Configuration Files

**backend/app/core/config.py:**
```python
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator

class Settings(BaseSettings):
    APP_NAME: str = "Test Case Generator API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    DATABASE_URL: str = "sqlite:///./testcasegen.db"
    SECRET_KEY: str = "HhkEbE1ndAUqwKTlfdytt9KpBJosZc9NVEbTohX4kDg"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4o"
    OPENAI_TEMPERATURE: float = 0.1
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024
    ALLOWED_EXTENSIONS: list = [".pdf", ".docx", ".txt"]
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "app.log"

    @field_validator("OPENAI_API_KEY", mode="before")
    @classmethod
    def validate_openai_key(cls, v):
        if not v:
            v = os.getenv("OPENAI_API_KEY")
        return v

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

#### Step 5: Database Setup

**backend/app/core/database.py:**
```python
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator
from .config import settings

engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_tables():
    Base.metadata.create_all(bind=engine)
```

#### Step 6: Database Models

**backend/app/models/user.py:**
```python
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..core.database import Base

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")
```

#### Step 7: API Schemas

**backend/app/schemas/user.py:**
```python
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, field_validator

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    password: str

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.isalnum():
            raise ValueError('Username must contain only alphanumeric characters')
        return v

class User(BaseModel):
    id: int
    username: str
    email: EmailStr
    full_name: str
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True
```

#### Step 8: Security Implementation

**backend/app/core/security.py:**
```python
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from fastapi.security import HTTPBearer
from .config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

#### Step 9: Service Layer

**backend/app/services/auth_service.py:**
```python
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from ..models import User
from ..schemas import UserCreate
from ..core.security import get_password_hash, verify_password

class AuthService:
    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        if AuthService.get_user_by_username(db, user_create.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )

        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            username=user_create.username,
            email=user_create.email,
            full_name=user_create.full_name,
            hashed_password=hashed_password
        )

        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        return db.query(User).filter(User.username == username).first()
```

#### Step 10: API Endpoints

**backend/app/api/v1/auth.py:**
```python
from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ...core.database import get_db
from ...core.security import create_access_token
from ...core.config import settings
from ...schemas import UserCreate, UserLogin, Token, User
from ...services import AuthService

router = APIRouter()

@router.post("/register", response_model=User, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    user = AuthService.create_user(db, user_data)
    return user

@router.post("/login", response_model=Token)
async def login(login_data: UserLogin, db: Session = Depends(get_db)):
    user = AuthService.authenticate_user(db, login_data.username, login_data.password)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}
```

#### Step 11: Main Application

**backend/app/main.py:**
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .core.config import settings
from .core.database import create_tables
from .api.v1 import auth, projects, test_cases

app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="AI-Powered Test Case Generator API"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(projects.router, prefix="/api/v1/projects", tags=["Projects"])
app.include_router(test_cases.router, prefix="/api/v1/test-cases", tags=["Test Cases"])

@app.on_event("startup")
async def startup_event():
    create_tables()

@app.get("/")
async def root():
    return {"message": "Test Case Generator API", "version": settings.APP_VERSION}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.APP_VERSION}
```

#### Step 12: Frontend Setup

**frontend/streamlit_app.py:**
```python
import streamlit as st
import requests
from typing import Optional, Dict, Any

API_BASE_URL = "http://localhost:8000/api/v1"

st.set_page_config(page_title="Test Case Generator", page_icon="🔧", layout="wide")

# Session state initialization
if 'access_token' not in st.session_state:
    st.session_state.access_token = None

def make_api_request(method: str, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict[str, Any]]:
    url = f"{API_BASE_URL}{endpoint}"
    headers = {}

    if st.session_state.access_token:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"

    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers)

        if response.status_code in [200, 201]:
            return response.json()
        else:
            st.error(f"API Error: {response.json().get('detail', 'Unknown error')}")
            return None
    except Exception as e:
        st.error(f"Request failed: {str(e)}")
        return None

# Main application logic
if not st.session_state.access_token:
    st.title("🔧 Test Case Generator")

    with st.form("login_form"):
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        login_button = st.form_submit_button("Login")

        if login_button and username and password:
            response = make_api_request("POST", "/auth/login", {
                "username": username,
                "password": password
            })
            if response:
                st.session_state.access_token = response["access_token"]
                st.rerun()
else:
    st.title("Welcome to Test Case Generator!")
    # Add your authenticated UI here
```

#### Step 13: Environment Configuration

**backend/.env:**
```env
APP_NAME=Test Case Generator API
DEBUG=true
SECRET_KEY=your-secret-key-for-development
OPENAI_API_KEY=your-openai-api-key-here
DATABASE_URL=sqlite:///./testcasegen.db
```

#### Step 14: Running the Application

```bash
# Terminal 1 - Backend
cd backend
python -m uvicorn app.main:app --reload --port 8000

# Terminal 2 - Frontend
cd frontend
streamlit run streamlit_app.py --server.port 8501
```

#### Step 15: Testing

1. **Open browser:** http://localhost:8501
2. **Register account:** Create new user
3. **Login:** Use credentials
4. **Create project:** Add new project
5. **Upload document:** Test file processing
6. **Generate test cases:** Use AI functionality

---

## 🎓 Key Learning Points

### 🏗️ Architecture Patterns Used

1. **Layered Architecture:**
   - API Layer (FastAPI routes)
   - Service Layer (Business logic)
   - Model Layer (Database)
   - Data Layer (SQLite)

2. **Dependency Injection:**
   - Database sessions
   - Authentication dependencies
   - Configuration settings

3. **Separation of Concerns:**
   - Models (data structure)
   - Schemas (validation)
   - Services (business logic)
   - APIs (HTTP interface)

### 🔧 Technologies Mastered

1. **FastAPI:** Modern Python web framework
2. **SQLAlchemy:** Database ORM
3. **Pydantic:** Data validation
4. **JWT:** Stateless authentication
5. **Streamlit:** Python web UI
6. **OpenAI API:** AI integration

### 🛡️ Security Concepts

1. **Password hashing** with bcrypt
2. **JWT tokens** for authentication
3. **Input validation** with Pydantic
4. **File upload security**
5. **Error handling** best practices

### 📊 Database Design

1. **Relational modeling**
2. **Foreign key relationships**
3. **Indexes for performance**
4. **Timestamps for auditing**
5. **Cascade operations**

---

## 🎯 Conclusion

You now have a complete understanding of:

1. **Every line of code** and its purpose
2. **Architecture decisions** and why they were made
3. **Security implementations** and best practices
4. **Database design** and relationships
5. **API design** and REST principles
6. **Frontend development** with Streamlit
7. **AI integration** with OpenAI
8. **Deployment** and production considerations

This knowledge enables you to:
- **Rebuild the entire project** from scratch
- **Explain every component** to others
- **Extend the functionality** with new features
- **Debug issues** effectively
- **Deploy to production** confidently

**You are now ready to be the expert on this codebase!** 🚀
