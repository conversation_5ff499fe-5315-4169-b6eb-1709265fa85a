"""
LLM Factory for creating different LLM instances.
"""
from typing import Any
from llama_index.llms.openai import OpenAI
from llama_index.llms.ollama import Ollama
from llama_index.llms.gemini import Gemini

from ..core.config import settings, LLMProvider
from ..utils.logger import logger

class LLMFactory:
    """Factory class for creating LLM instances."""
    
    @staticmethod
    def create_llm() -> Any:
        """Create LLM instance based on configuration."""
        provider = settings.LLM_PROVIDER
        
        try:
            if provider == LLMProvider.OPENAI:
                return LLMFactory._create_openai_llm()
            elif provider == LLMProvider.OLLAMA:
                return LLMFactory._create_ollama_llm()
            elif provider == LLMProvider.GEMINI:
                return LLMFactory._create_gemini_llm()
            else:
                raise ValueError(f"Unsupported LLM provider: {provider}")
                
        except Exception as e:
            logger.error(f"Failed to create LLM instance for provider {provider}: {e}")
            raise
    
    @staticmethod
    def _create_openai_llm() -> OpenAI:
        """Create OpenAI LLM instance."""
        if not settings.OPENAI_API_KEY:
            raise ValueError("OpenAI API key is required but not provided")
        
        return OpenAI(
            temperature=settings.OPENAI_TEMPERATURE,
            model=settings.OPENAI_MODEL,
            api_key=settings.OPENAI_API_KEY
        )
    
    @staticmethod
    def _create_ollama_llm() -> Ollama:
        """Create Ollama LLM instance."""
        return Ollama(
            model=settings.OLLAMA_MODEL,
            base_url=settings.OLLAMA_BASE_URL,
            request_timeout=settings.OLLAMA_REQUEST_TIMEOUT,
            context_window=settings.OLLAMA_CONTEXT_WINDOW,
        )
    
    @staticmethod
    def _create_gemini_llm() -> Gemini:
        """Create Gemini LLM instance."""
        if not settings.GOOGLE_API_KEY:
            raise ValueError("Google API key is required but not provided")
        
        return Gemini(
            model=settings.GEMINI_MODEL,
            api_key=settings.GOOGLE_API_KEY,
            temperature=settings.GEMINI_TEMPERATURE,
        )
    
    @staticmethod
    def get_provider_info() -> dict:
        """Get information about current LLM provider."""
        provider = settings.LLM_PROVIDER
        
        if provider == LLMProvider.OPENAI:
            return {
                "provider": "OpenAI",
                "model": settings.OPENAI_MODEL,
                "temperature": settings.OPENAI_TEMPERATURE,
                "api_key_configured": bool(settings.OPENAI_API_KEY)
            }
        elif provider == LLMProvider.OLLAMA:
            return {
                "provider": "Ollama",
                "model": settings.OLLAMA_MODEL,
                "base_url": settings.OLLAMA_BASE_URL,
                "context_window": settings.OLLAMA_CONTEXT_WINDOW
            }
        elif provider == LLMProvider.GEMINI:
            return {
                "provider": "Google Gemini",
                "model": settings.GEMINI_MODEL,
                "temperature": settings.GEMINI_TEMPERATURE,
                "api_key_configured": bool(settings.GOOGLE_API_KEY)
            }
        
        return {"provider": "Unknown"}