# Application Configuration
APP_NAME=Test Case Generator API
APP_VERSION=1.0.0
DEBUG=false

# Database Configuration
DATABASE_URL=sqlite:///./testcasegen.db

# Security Configuration
SECRET_KEY=HhkEbE1ndAUqwKTlfdytt9KpBJosZc9NVEbTohX4kDg
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4o
OPENAI_TEMPERATURE=0.1

# File Storage Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=[".pdf", ".docx", ".txt"]

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=app.log
