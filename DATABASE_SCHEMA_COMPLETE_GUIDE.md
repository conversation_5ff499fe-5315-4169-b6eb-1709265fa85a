# 🗄️ COMPLETE DATABASE SCHEMA GUIDE
## From Zero to Database Expert: Understanding Every Bit

---

## 📖 Table of Contents

1. [Database Fundamentals](#database-fundamentals)
2. [SQLAlchemy ORM Concepts](#sqlalchemy-orm-concepts)
3. [Complete Schema Overview](#complete-schema-overview)
4. [Table-by-Table Deep Dive](#table-by-table-deep-dive)
5. [Relationships Explained](#relationships-explained)
6. [Data Types and Constraints](#data-types-and-constraints)
7. [Indexes and Performance](#indexes-and-performance)
8. [Database Operations](#database-operations)
9. [Query Patterns](#query-patterns)
10. [Data Integrity and Validation](#data-integrity-and-validation)
11. [Migration and Schema Evolution](#migration-and-schema-evolution)
12. [Best Practices](#best-practices)

---

## 1. Database Fundamentals

### 🎯 **What is a Database?**

A database is like a **digital filing cabinet** that stores information in an organized way. Think of it as:

- **Filing Cabinet** = Database
- **Drawers** = Tables
- **Folders** = Rows (Records)
- **Labels on Folders** = Columns (Fields)

### 🏗️ **What is a Schema?**

A **schema** is the **blueprint** of your database. It defines:
- **What tables exist** (like what drawers are in your filing cabinet)
- **What columns each table has** (like what information goes on each label)
- **How tables connect to each other** (like how folders in different drawers relate)
- **What rules apply** (like "every folder must have a unique ID number")

### 📊 **Relational Database Concepts**

#### **Tables (Relations)**
```
Think of a table like a spreadsheet:

USERS TABLE:
+----+----------+-------------------+-------------+
| ID | USERNAME | EMAIL             | FULL_NAME   |
+----+----------+-------------------+-------------+
| 1  | john     | <EMAIL>    | John Doe    |
| 2  | jane     | <EMAIL>    | Jane Smith  |
+----+----------+-------------------+-------------+
```

#### **Rows (Records/Tuples)**
Each row represents **one complete item**:
- Row 1 = One complete user (John Doe)
- Row 2 = Another complete user (Jane Smith)

#### **Columns (Fields/Attributes)**
Each column represents **one piece of information**:
- ID column = User's unique identifier
- USERNAME column = User's login name
- EMAIL column = User's email address

#### **Primary Key**
A **unique identifier** for each row:
- Like a **social security number** for people
- **No two rows can have the same primary key**
- Usually called `id` and auto-increments (1, 2, 3, 4...)

#### **Foreign Key**
A **reference to another table's primary key**:
- Like saying "this project belongs to user #5"
- Creates **relationships between tables**
- Ensures **data integrity** (can't reference a user that doesn't exist)

### 🔗 **Why Use Relationships?**

Instead of storing everything in one giant table:

**❌ BAD - Everything in one table:**
```
+----+----------+-------------+--------------+------------------+
| ID | USERNAME | FULL_NAME   | PROJECT_NAME | PROJECT_DESC     |
+----+----------+-------------+--------------+------------------+
| 1  | john     | John Doe    | Website      | Company website  |
| 2  | john     | John Doe    | Mobile App   | iOS application  |
| 3  | jane     | Jane Smith  | API Service  | REST API         |
+----+----------+-------------+--------------+------------------+
```
**Problems:** Duplicate user data, hard to update, wastes space

**✅ GOOD - Separate related tables:**
```
USERS TABLE:
+----+----------+-------------+
| ID | USERNAME | FULL_NAME   |
+----+----------+-------------+
| 1  | john     | John Doe    |
| 2  | jane     | Jane Smith  |
+----+----------+-------------+

PROJECTS TABLE:
+----+--------------+------------------+----------+
| ID | NAME         | DESCRIPTION      | OWNER_ID |
+----+--------------+------------------+----------+
| 1  | Website      | Company website  | 1        |
| 2  | Mobile App   | iOS application  | 1        |
| 3  | API Service  | REST API         | 2        |
+----+--------------+------------------+----------+
```
**Benefits:** No duplication, easy to update, efficient storage

---

## 2. SQLAlchemy ORM Concepts

### 🐍 **What is an ORM?**

**ORM** = **Object-Relational Mapping**

It's a **translator** between Python objects and database tables:

```python
# Instead of writing SQL like this:
"SELECT * FROM users WHERE username = 'john'"

# You write Python like this:
user = db.query(User).filter(User.username == 'john').first()
```

### 🏗️ **SQLAlchemy Components**

#### **Base Class**
```python
from sqlalchemy.ext.declarative import declarative_base
Base = declarative_base()
```
- **Purpose:** Parent class for all database models
- **Think of it as:** The foundation that all tables are built on
- **What it provides:** Common functionality for all models

#### **Column Definition**
```python
from sqlalchemy import Column, Integer, String

class User(Base):
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
```

**Column Parameters Explained:**
- **`Integer`**: Data type (whole numbers like 1, 2, 3)
- **`String(50)`**: Text data type, maximum 50 characters
- **`primary_key=True`**: This column uniquely identifies each row
- **`unique=True`**: No two rows can have the same value
- **`nullable=False`**: This field is required (cannot be empty)

#### **Relationships**
```python
class User(Base):
    projects = relationship("Project", back_populates="owner")

class Project(Base):
    owner = relationship("User", back_populates="projects")
    owner_id = Column(Integer, ForeignKey("users.id"))
```

**Relationship Parameters:**
- **`"Project"`**: Name of the related model class
- **`back_populates="owner"`**: Creates bidirectional relationship
- **`ForeignKey("users.id")`**: References the `id` column in `users` table

---

## 3. Complete Schema Overview

### 🗺️ **Database Entity Relationship Diagram**

```
┌─────────────────┐
│     USERS       │
│ ┌─────────────┐ │
│ │ id (PK)     │ │
│ │ username    │ │
│ │ email       │ │
│ │ full_name   │ │
│ │ password    │ │
│ │ is_active   │ │
│ │ created_at  │ │
│ └─────────────┘ │
└─────────┬───────┘
          │ 1:N (One user has many projects)
          ▼
┌─────────────────┐
│    PROJECTS     │
│ ┌─────────────┐ │
│ │ id (PK)     │ │
│ │ name        │ │
│ │ description │ │
│ │ owner_id(FK)│ │────┐
│ │ created_at  │ │    │
│ └─────────────┘ │    │
└─────────┬───────┘    │
          │ 1:N        │ References
          ▼            │ users.id
┌─────────────────┐    │
│   DOCUMENTS     │    │
│ ┌─────────────┐ │    │
│ │ id (PK)     │ │    │
│ │ filename    │ │    │
│ │ file_path   │ │    │
│ │ status      │ │    │
│ │ project_id  │ │────┘
│ │ uploaded_by │ │
│ │ created_at  │ │
│ └─────────────┘ │
└─────────┬───────┘
          │ 1:N
          ▼
┌─────────────────┐
│  REQUIREMENTS   │
│ ┌─────────────┐ │
│ │ id (PK)     │ │
│ │ req_id      │ │
│ │ description │ │
│ │ document_id │ │
│ │ created_at  │ │
│ └─────────────┘ │
└─────────┬───────┘
          │ 1:N
          ▼
┌─────────────────┐
│   TEST_CASES    │
│ ┌─────────────┐ │
│ │ id (PK)     │ │
│ │ test_case_id│ │
│ │ title       │ │
│ │ description │ │
│ │ test_steps  │ │
│ │ expected    │ │
│ │ project_id  │ │
│ │ req_id      │ │
│ │ created_by  │ │
│ │ created_at  │ │
│ └─────────────┘ │
└─────────────────┘
```

### 📊 **Data Flow Through Tables**

```
USER CREATES PROJECT → PROJECT STORES DOCUMENTS → DOCUMENTS CONTAIN REQUIREMENTS → REQUIREMENTS GENERATE TEST CASES

1. User registers → Record in USERS table
2. User creates project → Record in PROJECTS table (linked to user)
3. User uploads document → Record in DOCUMENTS table (linked to project)
4. System processes document → Records in REQUIREMENTS table (linked to document)
5. AI generates test cases → Records in TEST_CASES table (linked to requirement and project)
```

---

## 4. Table-by-Table Deep Dive

### 👤 **USERS Table - Complete Analysis**

```python
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")
```

#### **Column-by-Column Explanation:**

**`id = Column(Integer, primary_key=True, index=True)`**
- **Data Type:** `Integer` - Whole numbers (1, 2, 3, 4...)
- **Purpose:** Unique identifier for each user
- **`primary_key=True`:** Makes this the main identifier
  - **Why needed:** Every table needs a unique way to identify rows
  - **Auto-increment:** Database automatically assigns 1, 2, 3, 4...
  - **Uniqueness:** No two users can have the same ID
- **`index=True`:** Creates database index for fast lookups
  - **Why important:** Makes queries like "find user with ID 5" super fast
  - **Performance:** Without index, database scans every row; with index, direct lookup

**`username = Column(String(50), unique=True, index=True, nullable=False)`**
- **Data Type:** `String(50)` - Text up to 50 characters
- **Purpose:** User's login name (like "john_doe")
- **`unique=True`:** No two users can have same username
  - **Why needed:** Users need unique login names
  - **Database enforces:** Prevents duplicate usernames automatically
- **`index=True`:** Fast username lookups during login
- **`nullable=False`:** Username is required
  - **Why needed:** Can't have a user without a username

**`email = Column(String(100), unique=True, index=True, nullable=False)`**
- **Data Type:** `String(100)` - Text up to 100 characters
- **Purpose:** User's email address
- **`unique=True`:** One email per user account
  - **Business rule:** Prevents multiple accounts with same email
- **`index=True`:** Fast email lookups for password reset, etc.
- **`nullable=False`:** Email is required for account creation

**`full_name = Column(String(100), nullable=False)`**
- **Data Type:** `String(100)` - User's display name
- **Purpose:** Human-readable name (like "John Doe")
- **`nullable=False`:** Full name is required
- **No unique constraint:** Multiple people can have same name

**`hashed_password = Column(String(255), nullable=False)`**
- **Data Type:** `String(255)` - Long text for password hash
- **Purpose:** Stores encrypted password (NEVER plain text)
- **Why 255 characters:** bcrypt hashes are about 60 characters, but allows for future algorithms
- **Security:** Original password cannot be recovered from hash
- **`nullable=False`:** Password is required

**`is_active = Column(Boolean, default=True)`**
- **Data Type:** `Boolean` - True/False value
- **Purpose:** Whether user account is enabled
- **`default=True`:** New users are active by default
- **Use cases:** Disable accounts without deleting data

**`is_superuser = Column(Boolean, default=False)`**
- **Data Type:** `Boolean` - True/False value
- **Purpose:** Whether user has admin privileges
- **`default=False`:** Regular users by default
- **Security:** Only specific users get admin access

**`created_at = Column(DateTime(timezone=True), server_default=func.now())`**
- **Data Type:** `DateTime(timezone=True)` - Date and time with timezone
- **Purpose:** When the user account was created
- **`server_default=func.now()`:** Database sets timestamp automatically
- **Why important:** Audit trail, account age tracking

**`updated_at = Column(DateTime(timezone=True), onupdate=func.now())`**
- **Data Type:** `DateTime(timezone=True)` - Date and time with timezone
- **Purpose:** When the user account was last modified
- **`onupdate=func.now()`:** Database updates timestamp on any change
- **Why important:** Track when user info was last changed

#### **USERS Table SQL Equivalent:**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_id ON users(id);
```

#### **Sample Data in USERS Table:**
```
+----+----------+-------------------+-------------+------------------+-----------+--------------+---------------------+---------------------+
| id | username | email             | full_name   | hashed_password  | is_active | is_superuser | created_at          | updated_at          |
+----+----------+-------------------+-------------+------------------+-----------+--------------+---------------------+---------------------+
| 1  | john     | <EMAIL>    | John Doe    | $2b$12$xyz...   | 1         | 0            | 2024-01-15 10:30:00 | 2024-01-15 10:30:00 |
| 2  | jane     | <EMAIL>    | Jane Smith  | $2b$12$abc...   | 1         | 0            | 2024-01-16 14:20:00 | 2024-01-16 14:20:00 |
| 3  | admin    | <EMAIL> | Admin User  | $2b$12$def...   | 1         | 1            | 2024-01-10 09:00:00 | 2024-01-20 16:45:00 |
+----+----------+-------------------+-------------+------------------+-----------+--------------+---------------------+---------------------+
```

### 📁 **PROJECTS Table - Complete Analysis**

```python
class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="projects")
    documents = relationship("Document", back_populates="project", cascade="all, delete-orphan")
    test_cases = relationship("TestCase", back_populates="project", cascade="all, delete-orphan")
```

#### **Column-by-Column Explanation:**

**`id = Column(Integer, primary_key=True, index=True)`**
- **Same as users.id:** Unique identifier for each project
- **Auto-increment:** 1, 2, 3, 4... assigned automatically
- **Primary key:** Main way to identify projects

**`name = Column(String(100), nullable=False, index=True)`**
- **Data Type:** `String(100)` - Project name up to 100 characters
- **Purpose:** Human-readable project name (like "E-commerce Website")
- **`nullable=False`:** Every project must have a name
- **`index=True`:** Fast project name searches
- **No unique constraint:** Users can have projects with same names

**`description = Column(Text, nullable=True)`**
- **Data Type:** `Text` - Long text field (unlimited length)
- **Purpose:** Detailed project description
- **`nullable=True`:** Description is optional
- **Why Text vs String:** Text allows very long descriptions

**`owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)`**
- **Data Type:** `Integer` - References users.id
- **Purpose:** Links project to the user who owns it
- **`ForeignKey("users.id")`:** Must match an existing user's ID
- **`nullable=False`:** Every project must have an owner
- **Database constraint:** Cannot reference non-existent user

#### **Foreign Key Relationship Explained:**

```
USERS Table:          PROJECTS Table:
+----+----------+     +----+-------------+----------+
| id | username |     | id | name        | owner_id |
+----+----------+     +----+-------------+----------+
| 1  | john     | ←── | 1  | Website     | 1        |
| 2  | jane     |     | 2  | Mobile App  | 1        |
+----+----------+     | 3  | API Service | 2        |
                      +----+-------------+----------+
```

**What this means:**
- Project 1 (Website) belongs to User 1 (john)
- Project 2 (Mobile App) belongs to User 1 (john)
- Project 3 (API Service) belongs to User 2 (jane)

#### **PROJECTS Table SQL Equivalent:**
```sql
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id)
);

CREATE INDEX idx_projects_name ON projects(name);
CREATE INDEX idx_projects_owner_id ON projects(owner_id);
```

#### **Sample Data in PROJECTS Table:**
```
+----+------------------+-------------------------+----------+---------------------+---------------------+
| id | name             | description             | owner_id | created_at          | updated_at          |
+----+------------------+-------------------------+----------+---------------------+---------------------+
| 1  | E-commerce Site  | Online shopping website | 1        | 2024-01-15 11:00:00 | 2024-01-15 11:00:00 |
| 2  | Mobile App       | iOS shopping app        | 1        | 2024-01-16 09:30:00 | 2024-01-16 09:30:00 |
| 3  | Payment API      | Payment processing API  | 2        | 2024-01-17 14:15:00 | 2024-01-17 14:15:00 |
+----+------------------+-------------------------+----------+---------------------+---------------------+
```

### 📄 **DOCUMENTS Table - Complete Analysis**

```python
class Document(Base):
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_type = Column(String(100), nullable=False)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.UPLOADED)
    processed_content = Column(Text, nullable=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    uploaded_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="documents")
    uploader = relationship("User")
    requirements = relationship("Requirement", back_populates="document", cascade="all, delete-orphan")
```

#### **Column-by-Column Explanation:**

**`filename = Column(String(255), nullable=False)`**
- **Purpose:** Actual filename stored on server (like "1_requirements.pdf")
- **Why different from original:** Prevents filename conflicts
- **Example:** User uploads "requirements.pdf", stored as "1_requirements.pdf"

**`original_filename = Column(String(255), nullable=False)`**
- **Purpose:** Original filename from user's computer
- **Why keep both:** Show user their original filename in UI
- **Example:** User sees "requirements.pdf" but server stores "1_requirements.pdf"

**`file_path = Column(String(500), nullable=False)`**
- **Purpose:** Complete path to file on server
- **Example:** "/uploads/user_1/project_1/1_requirements.pdf"
- **Why 500 characters:** Allows for deep directory structures

**`file_size = Column(Integer, nullable=False)`**
- **Purpose:** File size in bytes
- **Why store:** Check storage limits, show file size to users
- **Example:** 1048576 (1 MB in bytes)

**`content_type = Column(String(100), nullable=False)`**
- **Purpose:** MIME type of the file
- **Examples:** "application/pdf", "text/plain", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
- **Why important:** Determines how to process the file

**`status = Column(Enum(DocumentStatus), default=DocumentStatus.UPLOADED)`**
- **Data Type:** Enum - Predefined set of values
- **Possible values:** UPLOADED, PROCESSING, PROCESSED, ERROR
- **Purpose:** Track document processing state
- **Default:** UPLOADED (when first uploaded)

**Document Status Flow:**
```
UPLOADED → PROCESSING → PROCESSED (success)
    ↓
    └─→ ERROR (if processing fails)
```

**`processed_content = Column(Text, nullable=True)`**
- **Purpose:** Extracted text content from the file
- **Why store:** Avoid re-reading file for future operations
- **`nullable=True`:** Only filled after processing

**`project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)`**
- **Purpose:** Links document to its project
- **Foreign Key:** Must reference existing project
- **Business rule:** Documents belong to projects

**`uploaded_by = Column(Integer, ForeignKey("users.id"), nullable=False)`**
- **Purpose:** Tracks who uploaded the document
- **Why separate from project owner:** Project collaborators might upload
- **Audit trail:** Know who uploaded what

#### **DOCUMENTS Table Relationships:**

```
USERS Table:     PROJECTS Table:     DOCUMENTS Table:
+----+------+    +----+----------+   +----+----------+----------+-------------+
| id | name |    | id | name     |   | id | filename | project  | uploaded_by |
+----+------+    +----+----------+   +----+----------+----------+-------------+
| 1  | john | ←─ | 1  | Website  | ← | 1  | req1.pdf | 1        | 1           |
| 2  | jane |    | 2  | Mobile   |   | 2  | spec.doc | 1        | 2           |
+----+------+    +----+----------+   | 3  | api.txt  | 2        | 1           |
                                     +----+----------+----------+-------------+
```

**What this shows:**
- Document 1: "req1.pdf" in Website project, uploaded by john
- Document 2: "spec.doc" in Website project, uploaded by jane
- Document 3: "api.txt" in Mobile project, uploaded by john

### 📋 **REQUIREMENTS Table - Complete Analysis**

```python
class Requirement(Base):
    __tablename__ = "requirements"

    id = Column(Integer, primary_key=True, index=True)
    requirement_id = Column(String(50), nullable=False, index=True)
    description = Column(Text, nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    document = relationship("Document", back_populates="requirements")
    test_cases = relationship("TestCase", back_populates="requirement", cascade="all, delete-orphan")
```

#### **Column-by-Column Explanation:**

**`requirement_id = Column(String(50), nullable=False, index=True)`**
- **Purpose:** Human-readable requirement identifier (like "REQ-1", "REQ-2")
- **Why separate from id:** Business identifier vs database identifier
- **Example:** "REQ-1", "REQ-2", "REQ-10"
- **`index=True`:** Fast lookups by requirement ID
- **Not unique globally:** Different documents can have "REQ-1"

**`description = Column(Text, nullable=False)`**
- **Purpose:** Full text of the requirement
- **Data Type:** `Text` - Allows long requirement descriptions
- **Example:** "The system shall allow users to register with email and password"
- **`nullable=False`:** Every requirement must have description

**`document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)`**
- **Purpose:** Links requirement to source document
- **Why important:** Traceability from requirement back to original document
- **Foreign Key:** Must reference existing document

#### **REQUIREMENTS Table Data Flow:**

```
Document Processing Flow:
1. User uploads "requirements.pdf" → DOCUMENTS table
2. System reads file content → Extract requirements
3. Find patterns like "REQ-1: Description" → Parse requirements
4. Create records in REQUIREMENTS table → Link to document
```

#### **Sample Data in REQUIREMENTS Table:**
```
+----+----------------+----------------------------------------+-------------+---------------------+
| id | requirement_id | description                            | document_id | created_at          |
+----+----------------+----------------------------------------+-------------+---------------------+
| 1  | REQ-1          | User shall register with email        | 1           | 2024-01-15 12:00:00 |
| 2  | REQ-2          | User shall login with credentials     | 1           | 2024-01-15 12:00:00 |
| 3  | REQ-3          | System shall validate email format    | 1           | 2024-01-15 12:00:00 |
| 4  | REQ-1          | API shall return JSON responses       | 2           | 2024-01-16 10:30:00 |
+----+----------------+----------------------------------------+-------------+---------------------+
```

**Notice:** REQ-1 appears twice (different documents can have same requirement IDs)

### 🧪 **TEST_CASES Table - Complete Analysis**

```python
class TestCase(Base):
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(String(50), nullable=False, index=True)
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    preconditions = Column(Text, nullable=True)
    test_steps = Column(Text, nullable=True)
    expected_result = Column(Text, nullable=True)
    test_type = Column(Enum(TestCaseType), default=TestCaseType.FUNCTIONAL)
    priority = Column(Enum(TestCasePriority), default=TestCasePriority.MEDIUM)

    # Foreign Keys
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=True)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    project = relationship("Project", back_populates="test_cases")
    requirement = relationship("Requirement", back_populates="test_cases")
    creator = relationship("User")
```

#### **Column-by-Column Explanation:**

**`test_case_id = Column(String(50), nullable=False, index=True)`**
- **Purpose:** Human-readable test case identifier (like "TC1.1", "TC1.2")
- **Format:** Usually "TC" + requirement number + "." + test case number
- **Example:** "TC1.1" (first test case for REQ-1)
- **Why important:** Easy reference in documentation and reports

**`title = Column(String(500), nullable=False)`**
- **Purpose:** Short, descriptive test case title
- **Example:** "Verify user registration with valid email"
- **Length:** Up to 500 characters for detailed titles
- **`nullable=False`:** Every test case must have a title

**`description = Column(Text, nullable=True)`**
- **Purpose:** Detailed explanation of what the test validates
- **Example:** "This test verifies that users can successfully register..."
- **`nullable=True`:** Optional field

**`preconditions = Column(Text, nullable=True)`**
- **Purpose:** What must be true before running the test
- **Example:** "User is not already registered, registration page is accessible"
- **Why important:** Ensures test environment is properly set up

**`test_steps = Column(Text, nullable=True)`**
- **Purpose:** Step-by-step instructions to execute the test
- **Example:** "1. Navigate to registration page\n2. Enter valid email\n3. Click register"
- **Format:** Usually numbered steps

**`expected_result = Column(Text, nullable=True)`**
- **Purpose:** What should happen when test passes
- **Example:** "User is registered and redirected to login page"
- **Why important:** Clear pass/fail criteria

**`test_type = Column(Enum(TestCaseType), default=TestCaseType.FUNCTIONAL)`**
- **Data Type:** Enum with predefined values
- **Possible values:** FUNCTIONAL, NON_FUNCTIONAL, INTEGRATION, UNIT, ACCEPTANCE
- **Default:** FUNCTIONAL (most common type)
- **Purpose:** Categorize test cases by type

**`priority = Column(Enum(TestCasePriority), default=TestCasePriority.MEDIUM)`**
- **Data Type:** Enum with predefined values
- **Possible values:** LOW, MEDIUM, HIGH, CRITICAL
- **Default:** MEDIUM (balanced priority)
- **Purpose:** Prioritize test execution order

**`project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)`**
- **Purpose:** Links test case to its project
- **Why needed:** Test cases belong to projects
- **`nullable=False`:** Every test case must belong to a project

**`requirement_id = Column(Integer, ForeignKey("requirements.id"), nullable=True)`**
- **Purpose:** Links test case to source requirement
- **`nullable=True`:** Allows manual test cases without requirements
- **Traceability:** Shows which requirement the test case validates

**`created_by = Column(Integer, ForeignKey("users.id"), nullable=False)`**
- **Purpose:** Tracks who created the test case
- **Why important:** Audit trail, contact person for questions
- **Could be:** AI system (for generated) or user (for manual)

#### **TEST_CASES Table Relationships:**

```
Complete Data Flow:
USER → PROJECT → DOCUMENT → REQUIREMENT → TEST_CASE

Example:
john → E-commerce → requirements.pdf → REQ-1: User Registration → TC1.1: Test valid registration
                                                                 → TC1.2: Test invalid email
                                                                 → TC1.3: Test duplicate username
```

#### **Sample Data in TEST_CASES Table:**
```
+----+-------------+--------------------------------+------------+-------------+----------+----------+
| id | test_case_id| title                          | project_id | requirement | created  | priority |
+----+-------------+--------------------------------+------------+-------------+----------+----------+
| 1  | TC1.1       | Verify valid user registration | 1          | 1           | 1        | HIGH     |
| 2  | TC1.2       | Test invalid email format     | 1          | 1           | 1        | MEDIUM   |
| 3  | TC1.3       | Test duplicate username        | 1          | 1           | 1        | HIGH     |
| 4  | TC2.1       | Verify successful login        | 1          | 2           | 1        | CRITICAL |
+----+-------------+--------------------------------+------------+-------------+----------+----------+
```

---

## 5. Relationships Explained

### 🔗 **Understanding Database Relationships**

#### **One-to-Many (1:N) Relationships**

**User → Projects (One user has many projects)**
```python
# In User model
projects = relationship("Project", back_populates="owner", cascade="all, delete-orphan")

# In Project model
owner = relationship("User", back_populates="projects")
owner_id = Column(Integer, ForeignKey("users.id"))
```

**What this means:**
- One user can own multiple projects
- Each project has exactly one owner
- If user is deleted, all their projects are deleted (cascade)

**Visual Example:**
```
User: john (id=1)
├── Project: Website (id=1, owner_id=1)
├── Project: Mobile App (id=2, owner_id=1)
└── Project: API Service (id=3, owner_id=1)

User: jane (id=2)
└── Project: Analytics (id=4, owner_id=2)
```

#### **Cascade Behavior Explained**

**`cascade="all, delete-orphan"`**
- **"all":** When parent is deleted, delete all children
- **"delete-orphan":** When child is removed from parent, delete it

**Example:**
```python
# If we delete user john:
db.delete(john_user)
db.commit()

# Automatically deletes:
# - All projects owned by john
# - All documents in those projects
# - All requirements from those documents
# - All test cases for those requirements
```

#### **Foreign Key Constraints**

**What they prevent:**
```python
# ❌ This would fail:
project = Project(name="Test", owner_id=999)  # User 999 doesn't exist
db.add(project)
db.commit()  # Raises IntegrityError

# ❌ This would also fail:
db.delete(user)  # User has projects
db.commit()  # Raises IntegrityError (without cascade)
```

**What they ensure:**
- Data integrity (no orphaned records)
- Referential consistency
- Automatic cleanup with cascades

#### **Bidirectional Relationships**

**`back_populates` creates two-way access:**
```python
# From user to projects:
user = db.query(User).first()
user_projects = user.projects  # List of Project objects

# From project to user:
project = db.query(Project).first()
project_owner = project.owner  # User object
```

#### **Relationship Loading Strategies**

**Lazy Loading (default):**
```python
user = db.query(User).first()
projects = user.projects  # Triggers separate SQL query
```

**Eager Loading:**
```python
from sqlalchemy.orm import joinedload

user = db.query(User).options(joinedload(User.projects)).first()
projects = user.projects  # No additional SQL query
```

---

## 6. Data Types and Constraints

### 📊 **SQLAlchemy Data Types Explained**

#### **Numeric Types**

**`Integer`**
- **Range:** -2,147,483,648 to 2,147,483,647
- **Use for:** IDs, counts, ages, quantities
- **Storage:** 4 bytes
- **Example:** `user_id = Column(Integer)`

**`BigInteger`**
- **Range:** Much larger than Integer
- **Use for:** Large numbers, timestamps
- **Storage:** 8 bytes
- **Example:** `file_size = Column(BigInteger)`

**`Float`**
- **Type:** Decimal numbers
- **Use for:** Prices, percentages, measurements
- **Example:** `price = Column(Float)`

#### **String Types**

**`String(length)`**
- **Fixed maximum length**
- **Use for:** Names, emails, short text
- **Example:** `username = Column(String(50))`
- **Database:** VARCHAR(50)

**`Text`**
- **Unlimited length**
- **Use for:** Descriptions, content, long text
- **Example:** `description = Column(Text)`
- **Database:** TEXT

#### **Date/Time Types**

**`DateTime`**
- **Stores:** Date and time
- **Example:** `created_at = Column(DateTime)`
- **Format:** 2024-01-15 14:30:00

**`DateTime(timezone=True)`**
- **Stores:** Date, time, and timezone
- **Better for:** Global applications
- **Example:** `created_at = Column(DateTime(timezone=True))`

#### **Boolean Type**

**`Boolean`**
- **Values:** True/False
- **Database:** Usually 0/1 or TRUE/FALSE
- **Example:** `is_active = Column(Boolean, default=True)`

#### **Enum Types**

**Custom Enums:**
```python
import enum

class DocumentStatus(enum.Enum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"

status = Column(Enum(DocumentStatus), default=DocumentStatus.UPLOADED)
```

**Benefits:**
- **Restricted values:** Only predefined options allowed
- **Type safety:** Prevents invalid values
- **Self-documenting:** Clear what values are valid

### 🔒 **Database Constraints Explained**

#### **Primary Key Constraint**

**`primary_key=True`**
- **Purpose:** Uniquely identifies each row
- **Rules:** Cannot be NULL, must be unique
- **Auto-increment:** Usually automatically assigned
- **Example:** `id = Column(Integer, primary_key=True)`

#### **Unique Constraint**

**`unique=True`**
- **Purpose:** Ensures no duplicate values
- **Example:** `email = Column(String(100), unique=True)`
- **Database error:** Raised if duplicate attempted

#### **Not Null Constraint**

**`nullable=False`**
- **Purpose:** Field is required
- **Default:** `nullable=True` (field is optional)
- **Example:** `username = Column(String(50), nullable=False)`

#### **Foreign Key Constraint**

**`ForeignKey("table.column")`**
- **Purpose:** Links to another table
- **Rules:** Must reference existing value
- **Example:** `owner_id = Column(Integer, ForeignKey("users.id"))`

#### **Default Values**

**`default=value`**
- **Purpose:** Value used if none provided
- **Example:** `is_active = Column(Boolean, default=True)`

**`server_default=func.now()`**
- **Purpose:** Database sets the default
- **Example:** `created_at = Column(DateTime, server_default=func.now())`

## 7. Indexes and Performance

### 🚀 **What are Database Indexes?**

Think of a database index like an **index in a book**:

**Without Index (Table Scan):**
```
To find "John" in a phone book without index:
1. Start at page 1
2. Read every name on every page
3. Continue until you find "John"
4. Time: Very slow for large books
```

**With Index (Index Lookup):**
```
To find "John" with alphabetical index:
1. Go directly to "J" section
2. Find "John" immediately
3. Time: Very fast regardless of book size
```

### 📊 **Index Types in Our Schema**

#### **Primary Key Index (Automatic)**
```python
id = Column(Integer, primary_key=True, index=True)
```
- **Automatically created:** Every primary key gets an index
- **Purpose:** Fast lookups by ID
- **Query:** `SELECT * FROM users WHERE id = 5`
- **Performance:** O(log n) - very fast

#### **Unique Index (Automatic)**
```python
username = Column(String(50), unique=True, index=True)
email = Column(String(100), unique=True, index=True)
```
- **Automatically created:** Unique constraints create indexes
- **Purpose:** Fast lookups + uniqueness enforcement
- **Query:** `SELECT * FROM users WHERE username = 'john'`
- **Performance:** O(log n) - very fast

#### **Regular Index (Manual)**
```python
name = Column(String(100), nullable=False, index=True)
```
- **Manually specified:** `index=True`
- **Purpose:** Fast lookups on frequently queried columns
- **Query:** `SELECT * FROM projects WHERE name LIKE 'Web%'`
- **Performance:** Much faster than table scan

#### **Foreign Key Index (Recommended)**
```python
owner_id = Column(Integer, ForeignKey("users.id"), index=True)
```
- **Should always be indexed:** Foreign keys are frequently joined
- **Purpose:** Fast joins between tables
- **Query:** `SELECT * FROM projects WHERE owner_id = 1`
- **Performance:** Essential for good join performance

### 📈 **Performance Impact Examples**

#### **Without Index (Slow):**
```sql
-- Table scan: Reads every row
SELECT * FROM users WHERE email = '<EMAIL>';
-- Time: O(n) - gets slower as table grows
-- 1,000 users: ~1ms
-- 1,000,000 users: ~1000ms
```

#### **With Index (Fast):**
```sql
-- Index lookup: Direct access
SELECT * FROM users WHERE email = '<EMAIL>';
-- Time: O(log n) - stays fast as table grows
-- 1,000 users: ~0.1ms
-- 1,000,000 users: ~0.2ms
```

#### **Join Performance:**
```sql
-- Without index on owner_id (VERY SLOW):
SELECT p.name, u.username
FROM projects p
JOIN users u ON p.owner_id = u.id;
-- Time: O(n*m) - extremely slow

-- With index on owner_id (FAST):
SELECT p.name, u.username
FROM projects p
JOIN users u ON p.owner_id = u.id;
-- Time: O(n log m) - much faster
```

### 🎯 **Index Strategy in Our Schema**

#### **Users Table Indexes:**
```python
id = Column(Integer, primary_key=True, index=True)        # Primary key lookup
username = Column(String(50), unique=True, index=True)    # Login queries
email = Column(String(100), unique=True, index=True)      # Email lookup
```

**Why these indexes:**
- **id:** Primary key lookups (user profile, authorization)
- **username:** Login authentication
- **email:** Password reset, duplicate checking

#### **Projects Table Indexes:**
```python
id = Column(Integer, primary_key=True, index=True)        # Primary key lookup
name = Column(String(100), nullable=False, index=True)    # Project search
owner_id = Column(Integer, ForeignKey("users.id"))       # Should be indexed
```

**Why these indexes:**
- **id:** Project selection, document upload
- **name:** Project search and filtering
- **owner_id:** User's projects query (very common)

#### **Test Cases Table Indexes:**
```python
id = Column(Integer, primary_key=True, index=True)        # Primary key lookup
test_case_id = Column(String(50), nullable=False, index=True)  # Business ID lookup
project_id = Column(Integer, ForeignKey("projects.id"))   # Should be indexed
requirement_id = Column(Integer, ForeignKey("requirements.id"))  # Should be indexed
```

**Why these indexes:**
- **test_case_id:** Reference in documentation
- **project_id:** Get all test cases for project (very common)
- **requirement_id:** Requirement coverage analysis

---

## 8. Database Operations

### 🔧 **CRUD Operations Explained**

**CRUD** = **Create, Read, Update, Delete**

#### **CREATE Operations**

**Creating a User:**
```python
# Python/SQLAlchemy way:
new_user = User(
    username="john",
    email="<EMAIL>",
    full_name="John Doe",
    hashed_password="$2b$12$..."
)
db.add(new_user)
db.commit()
db.refresh(new_user)  # Gets the assigned ID

# Equivalent SQL:
INSERT INTO users (username, email, full_name, hashed_password, is_active, created_at)
VALUES ('john', '<EMAIL>', 'John Doe', '$2b$12$...', TRUE, NOW());
```

**Creating Related Records:**
```python
# Create project for existing user
project = Project(
    name="Website",
    description="Company website",
    owner_id=user.id  # Links to existing user
)
db.add(project)
db.commit()

# Or create through relationship:
user.projects.append(project)
db.commit()
```

#### **READ Operations**

**Simple Queries:**
```python
# Get user by ID
user = db.query(User).filter(User.id == 1).first()

# Get user by username
user = db.query(User).filter(User.username == "john").first()

# Get all active users
users = db.query(User).filter(User.is_active == True).all()

# Equivalent SQL:
SELECT * FROM users WHERE id = 1;
SELECT * FROM users WHERE username = 'john';
SELECT * FROM users WHERE is_active = TRUE;
```

**Complex Queries with Joins:**
```python
# Get projects with owner information
projects = db.query(Project).join(User).filter(User.username == "john").all()

# Get test cases with requirement and project info
test_cases = db.query(TestCase)\
    .join(Requirement)\
    .join(Project)\
    .filter(Project.owner_id == user_id)\
    .all()

# Equivalent SQL:
SELECT p.* FROM projects p
JOIN users u ON p.owner_id = u.id
WHERE u.username = 'john';

SELECT tc.* FROM test_cases tc
JOIN requirements r ON tc.requirement_id = r.id
JOIN projects p ON tc.project_id = p.id
WHERE p.owner_id = 1;
```

#### **UPDATE Operations**

**Simple Updates:**
```python
# Update user information
user = db.query(User).filter(User.id == 1).first()
user.full_name = "John Smith"
user.updated_at = datetime.utcnow()
db.commit()

# Bulk update
db.query(User).filter(User.is_active == False).update({
    User.is_active: True
})
db.commit()

# Equivalent SQL:
UPDATE users SET full_name = 'John Smith', updated_at = NOW() WHERE id = 1;
UPDATE users SET is_active = TRUE WHERE is_active = FALSE;
```

#### **DELETE Operations**

**Simple Delete:**
```python
# Delete specific user
user = db.query(User).filter(User.id == 1).first()
db.delete(user)
db.commit()

# Bulk delete
db.query(TestCase).filter(TestCase.project_id == 1).delete()
db.commit()

# Equivalent SQL:
DELETE FROM users WHERE id = 1;
DELETE FROM test_cases WHERE project_id = 1;
```

**Cascade Delete:**
```python
# Delete user (automatically deletes all projects, documents, etc.)
user = db.query(User).filter(User.id == 1).first()
db.delete(user)
db.commit()

# Because of cascade="all, delete-orphan", this also deletes:
# - All projects owned by user
# - All documents in those projects
# - All requirements from those documents
# - All test cases for those requirements
```

### 🔄 **Transaction Management**

#### **What is a Transaction?**

A transaction is a **group of operations** that either **all succeed** or **all fail**:

```python
# Example: Creating user with initial project
try:
    # Start transaction (implicit)
    user = User(username="john", email="<EMAIL>")
    db.add(user)
    db.flush()  # Gets user.id without committing

    project = Project(name="Default Project", owner_id=user.id)
    db.add(project)

    db.commit()  # Both user and project saved

except Exception as e:
    db.rollback()  # Neither user nor project saved
    raise
```

#### **ACID Properties**

**Atomicity:** All operations succeed or all fail
**Consistency:** Database rules are always enforced
**Isolation:** Concurrent transactions don't interfere
**Durability:** Committed changes are permanent

#### **Session Lifecycle:**
```python
# 1. Create session
db = SessionLocal()

try:
    # 2. Perform operations
    db.add(object)
    db.query(Model).filter(...).update(...)

    # 3. Commit changes
    db.commit()

except Exception:
    # 4. Rollback on error
    db.rollback()
    raise

finally:
    # 5. Close session
    db.close()
```

---

## 9. Query Patterns

### 🔍 **Common Query Patterns in Our Application**

#### **User Authorization Queries**

**Get User's Projects:**
```python
# Pattern: Filter by ownership
projects = db.query(Project).filter(Project.owner_id == user_id).all()

# Why important: Security - users only see their data
# Performance: Uses index on owner_id
```

**Verify Project Ownership:**
```python
# Pattern: Existence check with ownership
project = db.query(Project).filter(
    Project.id == project_id,
    Project.owner_id == user_id
).first()

if not project:
    raise HTTPException(status_code=404, detail="Project not found")

# Why important: Prevents unauthorized access
```

#### **Hierarchical Data Queries**

**Get Test Cases with Requirements:**
```python
# Pattern: Join with related data
test_cases = db.query(TestCase)\
    .join(Requirement)\
    .filter(TestCase.project_id == project_id)\
    .all()

# Access requirement data:
for tc in test_cases:
    print(f"Test Case: {tc.title}")
    print(f"Requirement: {tc.requirement.description}")
```

**Get Project Statistics:**
```python
# Pattern: Aggregate queries
from sqlalchemy import func

stats = db.query(
    func.count(Document.id).label('document_count'),
    func.count(Requirement.id).label('requirement_count'),
    func.count(TestCase.id).label('test_case_count')
).select_from(Project)\
.outerjoin(Document)\
.outerjoin(Requirement)\
.outerjoin(TestCase)\
.filter(Project.id == project_id)\
.first()

print(f"Documents: {stats.document_count}")
print(f"Requirements: {stats.requirement_count}")
print(f"Test Cases: {stats.test_case_count}")
```

#### **Search and Filter Patterns**

**Project Search:**
```python
# Pattern: Text search with LIKE
projects = db.query(Project)\
    .filter(Project.name.ilike(f"%{search_term}%"))\
    .filter(Project.owner_id == user_id)\
    .all()

# ilike = case-insensitive LIKE
# %term% = contains search term anywhere
```

**Test Case Filtering:**
```python
# Pattern: Multiple filter conditions
test_cases = db.query(TestCase)\
    .filter(TestCase.project_id == project_id)\
    .filter(TestCase.test_type == TestCaseType.FUNCTIONAL)\
    .filter(TestCase.priority.in_([TestCasePriority.HIGH, TestCasePriority.CRITICAL]))\
    .order_by(TestCase.priority.desc(), TestCase.created_at.asc())\
    .all()

# Multiple filters = AND condition
# in_() = SQL IN operator
# order_by() = Sort results
```

#### **Eager Loading Patterns**

**Load Related Data Efficiently:**
```python
from sqlalchemy.orm import joinedload, selectinload

# Joinedload: Single query with JOIN
users = db.query(User)\
    .options(joinedload(User.projects))\
    .all()

# Access projects without additional queries:
for user in users:
    for project in user.projects:  # No additional SQL
        print(project.name)

# Selectinload: Separate optimized query
projects = db.query(Project)\
    .options(selectinload(Project.documents))\
    .filter(Project.owner_id == user_id)\
    .all()
```

#### **Pagination Patterns**

**Paginated Results:**
```python
# Pattern: LIMIT and OFFSET
page = 1
per_page = 10
offset = (page - 1) * per_page

test_cases = db.query(TestCase)\
    .filter(TestCase.project_id == project_id)\
    .order_by(TestCase.created_at.desc())\
    .offset(offset)\
    .limit(per_page)\
    .all()

# Get total count for pagination info
total = db.query(TestCase)\
    .filter(TestCase.project_id == project_id)\
    .count()

total_pages = (total + per_page - 1) // per_page
```

## 10. Data Integrity and Validation

### 🛡️ **Database-Level Integrity**

#### **Referential Integrity**

**Foreign Key Constraints Prevent:**
```python
# ❌ This will fail with IntegrityError:
project = Project(name="Test", owner_id=999)  # User 999 doesn't exist
db.add(project)
db.commit()  # Raises: FOREIGN KEY constraint failed

# ❌ This will also fail:
user = db.query(User).filter(User.id == 1).first()
db.delete(user)  # User has projects
db.commit()  # Raises: FOREIGN KEY constraint failed (without cascade)
```

**What Foreign Keys Guarantee:**
- **No orphaned records:** Every project has a valid owner
- **Data consistency:** References always point to existing data
- **Cascade behavior:** Related data is cleaned up automatically

#### **Unique Constraints**

**Prevent Duplicate Data:**
```python
# ❌ This will fail:
user1 = User(username="john", email="<EMAIL>")
user2 = User(username="john", email="<EMAIL>")  # Same username
db.add(user1)
db.add(user2)
db.commit()  # Raises: UNIQUE constraint failed: users.username

# ❌ This will also fail:
user3 = User(username="jane", email="<EMAIL>")  # Same email
db.add(user3)
db.commit()  # Raises: UNIQUE constraint failed: users.email
```

#### **Not Null Constraints**

**Ensure Required Fields:**
```python
# ❌ This will fail:
user = User(email="<EMAIL>")  # Missing required username
db.add(user)
db.commit()  # Raises: NOT NULL constraint failed: users.username

# ❌ This will also fail:
project = Project(description="Test project")  # Missing required name
db.add(project)
db.commit()  # Raises: NOT NULL constraint failed: projects.name
```

### ✅ **Application-Level Validation**

#### **Pydantic Schema Validation**

**Before Database Operations:**
```python
# In schemas/user.py
class UserCreate(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    password: str

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters')
        if not v.isalnum():
            raise ValueError('Username must be alphanumeric')
        return v
```

**Validation Flow:**
```
1. Client sends data → 2. Pydantic validates → 3. Database operations → 4. Database constraints
                                ↓                           ↓                    ↓
                        Catches format errors    Catches business logic    Catches data integrity
                        (email format, etc.)     (duplicate check, etc.)   (foreign key, etc.)
```

#### **Business Logic Validation**

**In Service Layer:**
```python
# In services/auth_service.py
@staticmethod
def create_user(db: Session, user_create: UserCreate) -> User:
    # Check business rules
    if AuthService.get_user_by_username(db, user_create.username):
        raise HTTPException(status_code=400, detail="Username already exists")

    if AuthService.get_user_by_email(db, user_create.email):
        raise HTTPException(status_code=400, detail="Email already exists")

    # Validation passed, create user
    hashed_password = get_password_hash(user_create.password)
    db_user = User(...)
    db.add(db_user)
    db.commit()
    return db_user
```

### 🔒 **Data Security Considerations**

#### **Password Security**

**Never Store Plain Text:**
```python
# ❌ NEVER do this:
user = User(password="plaintext123")

# ✅ Always hash passwords:
hashed = get_password_hash("plaintext123")
user = User(hashed_password=hashed)
```

**Password Hashing with bcrypt:**
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Hash password
hashed = pwd_context.hash("user_password")
# Result: "$2b$12$xyz..." (60 characters)

# Verify password
is_valid = pwd_context.verify("user_password", hashed)
# Result: True or False
```

#### **SQL Injection Prevention**

**SQLAlchemy ORM Prevents SQL Injection:**
```python
# ✅ Safe (parameterized query):
user = db.query(User).filter(User.username == username).first()

# ❌ Dangerous (if using raw SQL):
# query = f"SELECT * FROM users WHERE username = '{username}'"
# This allows SQL injection attacks

# ✅ Safe raw SQL (if needed):
result = db.execute(
    text("SELECT * FROM users WHERE username = :username"),
    {"username": username}
)
```

---

## 11. Migration and Schema Evolution

### 🔄 **Database Migrations Explained**

#### **What are Migrations?**

Migrations are **scripts that change database structure** over time:

```
Version 1: Initial schema
├── users table
└── projects table

Version 2: Add documents
├── users table
├── projects table
└── documents table (NEW)

Version 3: Add requirements
├── users table
├── projects table
├── documents table
└── requirements table (NEW)
```

#### **Why Migrations are Important**

**Without Migrations (Problems):**
- Manual schema changes on each server
- Risk of inconsistent database structures
- No version control for database changes
- Difficult to rollback changes

**With Migrations (Benefits):**
- Automated schema updates
- Version controlled database changes
- Consistent structure across environments
- Easy rollback capability

#### **Alembic Migration Example**

**Creating a Migration:**
```bash
# Generate migration for new column
alembic revision --autogenerate -m "Add user phone number"
```

**Generated Migration File:**
```python
# migrations/versions/001_add_user_phone.py
def upgrade():
    # Add phone column
    op.add_column('users', sa.Column('phone', sa.String(20), nullable=True))

def downgrade():
    # Remove phone column
    op.drop_column('users', 'phone')
```

**Applying Migration:**
```bash
# Apply migration to database
alembic upgrade head
```

#### **Common Migration Operations**

**Add Column:**
```python
def upgrade():
    op.add_column('users', sa.Column('phone', sa.String(20), nullable=True))
```

**Remove Column:**
```python
def upgrade():
    op.drop_column('users', 'phone')
```

**Add Index:**
```python
def upgrade():
    op.create_index('idx_users_phone', 'users', ['phone'])
```

**Add Foreign Key:**
```python
def upgrade():
    op.create_foreign_key('fk_projects_owner', 'projects', 'users', ['owner_id'], ['id'])
```

### 📊 **Schema Versioning Strategy**

#### **Development Workflow:**
```
1. Developer changes model → 2. Generate migration → 3. Review migration → 4. Apply to dev DB
                                      ↓                      ↓                    ↓
5. Test changes → 6. Commit migration → 7. Deploy to staging → 8. Deploy to production
```

#### **Best Practices:**
- **Always review** generated migrations
- **Test migrations** on copy of production data
- **Backup database** before major migrations
- **Plan rollback strategy** for each migration

---

## 12. Best Practices

### 🎯 **Database Design Best Practices**

#### **Naming Conventions**

**Table Names:**
- **Plural nouns:** `users`, `projects`, `test_cases`
- **Snake_case:** `test_cases` not `TestCases`
- **Descriptive:** Clear what the table contains

**Column Names:**
- **Snake_case:** `created_at`, `owner_id`
- **Descriptive:** `hashed_password` not `pwd`
- **Consistent:** Always use `id` for primary keys

**Foreign Key Names:**
- **Pattern:** `{referenced_table}_id`
- **Examples:** `owner_id`, `project_id`, `document_id`

#### **Data Type Choices**

**String Lengths:**
```python
# ✅ Appropriate lengths:
username = Column(String(50))      # Reasonable username length
email = Column(String(100))        # Standard email length
title = Column(String(500))        # Long enough for titles
description = Column(Text)         # Unlimited for descriptions

# ❌ Poor choices:
username = Column(String(10))      # Too short
email = Column(String(1000))       # Unnecessarily long
```

**Numeric Types:**
```python
# ✅ Appropriate types:
id = Column(Integer)               # Standard for IDs
file_size = Column(BigInteger)     # Large numbers
price = Column(Numeric(10, 2))     # Exact decimal for money

# ❌ Poor choices:
id = Column(String(10))            # ID should be numeric
price = Column(Float)              # Imprecise for money
```

#### **Index Strategy**

**Always Index:**
- Primary keys (automatic)
- Foreign keys
- Unique constraints (automatic)
- Frequently queried columns

**Consider Indexing:**
- Columns used in WHERE clauses
- Columns used in ORDER BY
- Columns used in JOIN conditions

**Don't Over-Index:**
- Indexes slow down INSERT/UPDATE/DELETE
- Each index uses storage space
- Only index what you actually query

#### **Relationship Design**

**Use Appropriate Relationships:**
```python
# ✅ One-to-Many (User → Projects):
class User(Base):
    projects = relationship("Project", back_populates="owner")

class Project(Base):
    owner_id = Column(Integer, ForeignKey("users.id"))
    owner = relationship("User", back_populates="projects")

# ✅ Many-to-Many (if needed):
# Use association table for many-to-many relationships
```

**Cascade Appropriately:**
```python
# ✅ Cascade delete for owned data:
projects = relationship("Project", cascade="all, delete-orphan")

# ❌ Don't cascade for referenced data:
# creator = relationship("User", cascade="all, delete-orphan")  # Wrong!
```

### 🚀 **Performance Best Practices**

#### **Query Optimization**

**Use Eager Loading:**
```python
# ✅ Load related data efficiently:
users = db.query(User).options(joinedload(User.projects)).all()

# ❌ N+1 query problem:
users = db.query(User).all()
for user in users:
    projects = user.projects  # Separate query for each user
```

**Use Appropriate Filters:**
```python
# ✅ Use indexed columns in WHERE:
user = db.query(User).filter(User.username == "john").first()

# ❌ Avoid functions in WHERE:
# users = db.query(User).filter(func.lower(User.username) == "john").all()
```

**Limit Result Sets:**
```python
# ✅ Use pagination for large results:
test_cases = db.query(TestCase).limit(50).offset(page * 50).all()

# ❌ Don't load everything:
# test_cases = db.query(TestCase).all()  # Could be millions of rows
```

#### **Connection Management**

**Use Connection Pooling:**
```python
# ✅ SQLAlchemy handles this automatically:
engine = create_engine(DATABASE_URL, pool_size=20, max_overflow=30)

# Connection pool reuses connections efficiently
```

**Close Sessions Properly:**
```python
# ✅ Always close sessions:
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()  # Always closes, even on exception
```

### 🔒 **Security Best Practices**

#### **Access Control**

**Row-Level Security:**
```python
# ✅ Always filter by ownership:
projects = db.query(Project).filter(Project.owner_id == current_user.id).all()

# ❌ Never expose all data:
# projects = db.query(Project).all()  # Shows everyone's projects
```

**Input Validation:**
```python
# ✅ Validate all inputs:
@field_validator('email')
@classmethod
def validate_email(cls, v):
    if '@' not in v:
        raise ValueError('Invalid email format')
    return v

# ✅ Use parameterized queries (SQLAlchemy does this):
user = db.query(User).filter(User.id == user_id).first()
```

#### **Data Protection**

**Encrypt Sensitive Data:**
```python
# ✅ Hash passwords:
hashed_password = Column(String(255))  # Never store plain text

# ✅ Consider encrypting PII:
# encrypted_ssn = Column(String(255))  # If storing sensitive data
```

**Audit Trails:**
```python
# ✅ Track changes:
created_at = Column(DateTime, server_default=func.now())
updated_at = Column(DateTime, onupdate=func.now())
created_by = Column(Integer, ForeignKey("users.id"))
```

---

## 🎓 **Database Mastery Checklist**

### ✅ **Fundamental Concepts**
- [ ] Understand what databases and schemas are
- [ ] Know the difference between tables, rows, and columns
- [ ] Understand primary keys and foreign keys
- [ ] Grasp the concept of relationships (1:1, 1:N, N:N)
- [ ] Know what indexes are and why they matter

### ✅ **SQLAlchemy ORM Mastery**
- [ ] Understand what ORM means and its benefits
- [ ] Know how to define models with Column types
- [ ] Understand relationship definitions and back_populates
- [ ] Know cascade behavior and when to use it
- [ ] Understand session management and transactions

### ✅ **Schema Design Mastery**
- [ ] Can design normalized database schemas
- [ ] Understand when and how to use foreign keys
- [ ] Know appropriate data types for different use cases
- [ ] Understand constraint types and their purposes
- [ ] Can design efficient relationship structures

### ✅ **Query Mastery**
- [ ] Can write basic CRUD operations
- [ ] Understand joins and how to query related data
- [ ] Know how to use filters, ordering, and pagination
- [ ] Understand eager loading vs lazy loading
- [ ] Can write efficient queries for common patterns

### ✅ **Performance Mastery**
- [ ] Understand index strategy and when to use indexes
- [ ] Know how to identify and solve N+1 query problems
- [ ] Understand query optimization techniques
- [ ] Know connection pooling and session management
- [ ] Can design schemas for performance

### ✅ **Security Mastery**
- [ ] Understand SQL injection and how ORMs prevent it
- [ ] Know how to implement row-level security
- [ ] Understand password hashing and sensitive data protection
- [ ] Know input validation best practices
- [ ] Understand audit trail implementation

### ✅ **Advanced Concepts**
- [ ] Understand database migrations and schema evolution
- [ ] Know transaction management and ACID properties
- [ ] Understand different isolation levels
- [ ] Know backup and recovery strategies
- [ ] Understand database monitoring and optimization

---

## 🎉 **CONGRATULATIONS!**

**You now have COMPLETE MASTERY of the database schema!**

### 🏆 **What You've Achieved:**

✅ **Complete Understanding** of every table, column, and relationship
✅ **Deep Knowledge** of SQLAlchemy ORM concepts and patterns
✅ **Expert-Level** query writing and optimization skills
✅ **Professional** database design and security practices
✅ **Production-Ready** knowledge of performance and scalability

### 🚀 **You Can Now:**

- **Design database schemas** from scratch
- **Optimize query performance** for any application
- **Implement security best practices** for data protection
- **Debug database issues** efficiently
- **Explain database concepts** to other developers
- **Make architectural decisions** about data storage
- **Scale databases** for production applications

**You are now a DATABASE EXPERT!** 🎯✨

This comprehensive guide has taken you from absolute beginner (level 0) to complete database mastery. You understand every single aspect of the schema, from basic concepts to advanced optimization techniques.

Use this knowledge to build robust, scalable, and secure applications! 🚀
