2025-07-11 21:56:51,169 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-11 21:56:51,302 - app.utils.logger - INFO - Database tables created successfully
2025-07-11 21:56:51,302 - app.utils.logger - INFO - Application startup completed
2025-07-11 21:58:52,222 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-11 21:58:52,717 - app.utils.logger - INFO - Created new user: admintest
2025-07-11 21:58:52,719 - app.utils.logger - INFO - User registered successfully: admintest
2025-07-11 21:59:08,554 - app.utils.logger - INFO - User authenticated: admintest
2025-07-11 21:59:08,568 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-11 21:59:57,197 - app.utils.logger - INFO - Project created: testing by user admintest
2025-07-11 22:02:16,978 - app.utils.logger - INFO - Document uploaded: 1_test_requirements.txt for project 1
2025-07-11 22:02:16,978 - app.utils.logger - INFO - Document uploaded: 1_test_requirements.txt to project 1
2025-07-11 22:02:19,033 - app.utils.logger - INFO - Processed document 1: 10 requirements found
2025-07-11 22:02:19,034 - app.utils.logger - INFO - Document processed: 1, found 10 requirements
2025-07-11 22:02:49,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:02:49,292 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-1
2025-07-11 22:02:55,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:02:55,490 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-2
2025-07-11 22:03:10,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:03:10,216 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-3
2025-07-11 22:03:22,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:03:22,182 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-4
2025-07-11 22:03:32,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:03:32,024 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-5
2025-07-11 22:03:44,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:03:44,928 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-6
2025-07-11 22:03:54,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:03:54,328 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-7
2025-07-11 22:04:02,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:04:02,665 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-8
2025-07-11 22:04:14,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:04:14,228 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-9
2025-07-11 22:04:30,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:04:30,363 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-10
2025-07-11 22:04:30,380 - app.utils.logger - INFO - Generated 50 total test cases for project 1
2025-07-11 22:04:30,380 - app.utils.logger - INFO - Generated 50 test cases for project 1
2025-07-11 22:12:19,829 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-11 22:12:22,495 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-11 22:12:22,495 - app.utils.logger - INFO - Database tables created successfully
2025-07-11 22:12:22,495 - app.utils.logger - INFO - Application startup completed
2025-07-11 22:12:32,067 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-11 22:12:33,954 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-11 22:12:33,959 - app.utils.logger - INFO - Database tables created successfully
2025-07-11 22:12:33,959 - app.utils.logger - INFO - Application startup completed
2025-07-11 22:12:47,502 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-11 22:12:49,330 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-11 22:12:49,331 - app.utils.logger - INFO - Database tables created successfully
2025-07-11 22:12:49,332 - app.utils.logger - INFO - Application startup completed
2025-07-11 22:13:05,510 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-11 22:13:07,454 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-11 22:13:07,457 - app.utils.logger - INFO - Database tables created successfully
2025-07-11 22:13:07,457 - app.utils.logger - INFO - Application startup completed
2025-07-11 22:14:04,434 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-11 22:14:04,823 - app.utils.logger - INFO - User authenticated: admintest
2025-07-11 22:14:04,862 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-11 22:14:44,286 - app.utils.logger - INFO - Document uploaded: 2_test_requirements.txt for project 1
2025-07-11 22:14:44,288 - app.utils.logger - INFO - Document uploaded: 2_test_requirements.txt to project 1
2025-07-11 22:14:46,367 - app.utils.logger - INFO - Processed document 2: 3 requirements found
2025-07-11 22:14:46,367 - app.utils.logger - INFO - Document processed: 2, found 3 requirements
2025-07-11 22:15:04,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:04,405 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-1
2025-07-11 22:15:14,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:14,580 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-2
2025-07-11 22:15:27,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:27,595 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-3
2025-07-11 22:15:38,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:38,314 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-4
2025-07-11 22:15:45,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:45,868 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-5
2025-07-11 22:15:55,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:15:55,783 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-6
2025-07-11 22:16:04,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:04,587 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-7
2025-07-11 22:16:15,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:15,177 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-8
2025-07-11 22:16:23,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:23,402 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-9
2025-07-11 22:16:39,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:39,773 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-10
2025-07-11 22:16:48,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:48,089 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-1
2025-07-11 22:16:57,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:16:57,412 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-2
2025-07-11 22:17:06,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-11 22:17:06,486 - app.utils.logger - INFO - Generated 5 test cases for requirement REQ-3
2025-07-11 22:17:06,514 - app.utils.logger - INFO - Generated 65 total test cases for project 1
2025-07-11 22:17:06,515 - app.utils.logger - INFO - Generated 65 test cases for project 1
2025-07-11 22:19:31,886 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-14 11:59:49,267 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-14 11:59:49,270 - app.utils.logger - INFO - Database tables created successfully
2025-07-14 11:59:49,270 - app.utils.logger - INFO - Application startup completed
2025-07-14 12:00:05,042 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-14 12:00:05,414 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 12:00:05,437 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 12:00:07,797 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 12:00:07,797 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 12:04:46,432 - app.utils.logger - INFO - Shutting down Test Case Generator API...
2025-07-14 12:13:00,493 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-14 12:13:00,508 - app.utils.logger - INFO - Database tables created successfully
2025-07-14 12:13:00,508 - app.utils.logger - INFO - Application startup completed
2025-07-14 12:13:21,822 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-14 12:13:22,249 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 12:13:22,283 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 12:22:50,197 - app.utils.logger - INFO - File deleted: uploads\1\1\1_test_requirements.txt
2025-07-14 12:22:50,236 - app.utils.logger - INFO - Document 1 deleted from database
2025-07-14 12:22:50,237 - app.utils.logger - INFO - Document 1 deleted by user admintest
2025-07-14 12:23:12,415 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 12:23:12,416 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 12:45:04,937 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-14 12:45:04,946 - app.utils.logger - INFO - Database tables created successfully
2025-07-14 12:45:04,947 - app.utils.logger - INFO - Application startup completed
2025-07-14 12:45:22,301 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-14 12:45:22,520 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 12:45:22,544 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 13:28:12,013 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 13:28:12,016 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 13:28:42,176 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 13:28:42,176 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 19:16:09,327 - app.utils.logger - INFO - Starting Test Case Generator API...
2025-07-14 19:16:09,327 - app.utils.logger - INFO - Database tables created successfully
2025-07-14 19:16:09,327 - app.utils.logger - INFO - Application startup completed
2025-07-14 19:16:24,382 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\anaconda3\envs\testcasegen\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-14 19:16:24,811 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 19:16:24,819 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 19:23:51,076 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 19:23:51,081 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 19:31:04,017 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 19:31:04,018 - app.utils.logger - INFO - User logged in successfully: admintest
2025-07-14 19:32:23,120 - app.utils.logger - INFO - User authenticated: admintest
2025-07-14 19:32:23,121 - app.utils.logger - INFO - User logged in successfully: admintest
