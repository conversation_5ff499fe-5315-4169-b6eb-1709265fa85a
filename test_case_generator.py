import logging
import sys
import os
import json
import re
from pathlib import Path
from typing import List, Optional
from datetime import datetime
from llama_index.core import SimpleDirectoryReader, Document
from llama_index.llms.openai import OpenAI

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_case_generator.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Configuration
class Config:
    DATA_PATH = r"D:\atliq_work\coach\test_case\testcasegen_ai\data"
    OUTPUT_PATH = "output"
    DEFAULT_TEST_CASES_PER_REQ = 5
    MODEL_NAME = "gpt-4o"
    TEMPERATURE = 0.1
    
    @classmethod
    def validate(cls):
        os.makedirs(cls.OUTPUT_PATH, exist_ok=True)
        if not os.path.exists(cls.DATA_PATH):
            raise FileNotFoundError(f"Data path not found: {cls.DATA_PATH}")

# Requirement class
class Requirement:
    def __init__(self, id: str, description: str, source_file: Optional[str] = None):
        self.id = id
        self.description = description
        self.source_file = source_file

# Test case class
class TestCase:
    def __init__(self, id: str, requirement_id: str, title: str):
        self.id = id
        self.requirement_id = requirement_id
        self.title = title

# Requirement parser
class RequirementParser:
    def __init__(self):
        self.requirement_pattern = r'REQ-(\d+):\s*(.*?)(?=\nREQ-\d+:|\Z)'
    
    def parse_requirements(self, documents: List[Document]) -> List[Requirement]:
        requirements = []
        for doc in documents:
            content = doc.text
            source_file = getattr(doc, 'metadata', {}).get('file_name', 'unknown')
            matches = re.findall(self.requirement_pattern, content, re.MULTILINE | re.DOTALL)
            for req_num, description in matches:
                req_id = f"REQ-{req_num}"
                description = description.strip()
                requirements.append(Requirement(id=req_id, description=description, source_file=source_file))
        logger.info(f"Parsed {len(requirements)} requirements")
        return requirements

# Test case generator
class TestCaseGenerator:
    def __init__(self):
        self.llm = OpenAI(temperature=Config.TEMPERATURE, model=Config.MODEL_NAME)
    
    def generate_test_cases(self, requirements: List[Requirement]) -> List[TestCase]:
        all_test_cases = []
        logger.info(f"Generating test cases for {len(requirements)} requirements")
        for req in requirements:
            try:
                test_cases = self._generate_test_cases_for_requirement(req)
                all_test_cases.extend(test_cases)
                logger.info(f"Generated {len(test_cases)} test cases for {req.id}")
            except Exception as e:
                logger.error(f"Error generating test cases for {req.id}: {str(e)}")
                all_test_cases.append(self._create_fallback_test_case(req))
        return all_test_cases
    
    def _generate_test_cases_for_requirement(self, requirement: Requirement) -> List[TestCase]:
        prompt = f"""
You are a senior QA engineer tasked with creating test cases for a software system. 
Generate {Config.DEFAULT_TEST_CASES_PER_REQ} test cases for the following requirement:

REQUIREMENT ID: {requirement.id}
DESCRIPTION: {requirement.description}

For each test case, provide:
1. Test Case ID (format: TC{requirement.id.split('-')[1]}.X, e.g., TC1.1, TC1.2, no leading zeros)
2. Test Case Title (descriptive, concise, starting with 'Verify', 'Confirm', or 'Test', matching the style of: 'Verify that a new user can successfully register with valid information')

Focus on:
- Positive test scenarios (e.g., valid inputs, expected behavior)
- Negative test scenarios (e.g., invalid inputs, error handling)
- Edge cases (e.g., boundary values, extreme conditions)
- Boundary conditions (e.g., maximum/minimum limits)

Examples of good test case titles:
- Verify that a new user can successfully register with valid information
- Confirm that a registered user can log in with correct credentials
- Test that filtering options correctly narrow down product listings

Format your response as JSON:
{{
    "test_cases": [
        {{
            "id": "TC{requirement.id.split('-')[1]}.1",
            "title": "Verify that a new user can successfully register with valid information"
        }}
    ]
}}
"""
        response = self.llm.complete(prompt)
        return self._parse_test_case_response(response.text, requirement)
    
    def _parse_test_case_response(self, response_text: str, requirement: Requirement) -> List[TestCase]:
        test_cases = []
        try:
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_data = json.loads(json_match.group())
                for idx, tc_data in enumerate(json_data.get('test_cases', []), 1):
                    tc_id = tc_data.get('id', f"TC{requirement.id.split('-')[1]}.{idx}")
                    tc_id = re.sub(r'\.0(\d)$', r'.\1', tc_id)  # Remove leading zero
                    test_cases.append(TestCase(
                        id=tc_id,
                        requirement_id=requirement.id,
                        title=tc_data.get('title', 'Generated Test Case')
                    ))
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error parsing test case response: {str(e)}")
            test_cases.append(self._create_fallback_test_case(requirement))
        return test_cases
    
    def _create_fallback_test_case(self, requirement: Requirement) -> TestCase:
        return TestCase(
            id=f"TC{requirement.id.split('-')[1]}.1",
            requirement_id=requirement.id,
            title=f"Verify {requirement.description[:50]}"
        )

# Output manager
class OutputManager:
    def __init__(self, output_path: str = Config.OUTPUT_PATH):
        self.output_path = Path(output_path)
        self.output_path.mkdir(exist_ok=True)
    
    def save_report(self, requirements: List[Requirement], test_cases: List[TestCase]):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        md_file = self.output_path / f"test_case_report_{timestamp}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(self._generate_markdown_report(requirements, test_cases))
        logger.info(f"Report saved: {md_file}")
    
    def _generate_markdown_report(self, requirements: List[Requirement], test_cases: List[TestCase]) -> str:
        md_content = ""
        tc_by_req = {}
        for tc in test_cases:
            if tc.requirement_id not in tc_by_req:
                tc_by_req[tc.requirement_id] = []
            tc_by_req[tc.requirement_id].append(tc)
        
        for req in requirements:
            md_content += f"{req.id}: {req.description}\n"
            for tc in tc_by_req.get(req.id, []):
                tc_id_formatted = re.sub(r'TC(\d+)_\d+', r'TC\1.', tc.id)
                tc_id_formatted = re.sub(r'\.0(\d)$', r'.\1', tc_id_formatted)
                md_content += f"{tc_id_formatted}: {tc.title}\n"
            md_content += "\n"
        
        return md_content

# Main orchestrator
class TestCaseOrchestrator:
    def __init__(self):
        Config.validate()
        self.parser = RequirementParser()
        self.generator = TestCaseGenerator()
        self.output_manager = OutputManager()
    
    def process_documents(self, data_path: str = None):
        data_path = data_path or Config.DATA_PATH
        logger.info("Starting test case generation pipeline...")
        logger.info("Loading documents...")
        documents = SimpleDirectoryReader(data_path).load_data()
        logger.info("Parsing requirements...")
        requirements = self.parser.parse_requirements(documents)
        logger.info(f"Found {len(requirements)} requirements")
        logger.info("Generating test cases...")
        test_cases = self.generator.generate_test_cases(requirements)
        logger.info("Generating report...")
        self.output_manager.save_report(requirements, test_cases)
        logger.info(f"Pipeline completed: {len(test_cases)} test cases generated")

# Main function
def main():
    orchestrator = TestCaseOrchestrator()
    try:
        orchestrator.process_documents()
        print(f"\n✅ Processing completed successfully!")
    except Exception as e:
        logger.error(f"Error in processing: {str(e)}")

if __name__ == "__main__":
    main()